<template>
  <div class="progress-component" :style="containerStyle">
    <!-- 水平进度条 -->
    <div v-if="config.type === 'horizontal'" class="progress-horizontal">
      <div class="progress-label" v-if="showLabel">
        <span class="label-text">{{ labelText }}</span>
        <span class="progress-value">{{ displayValue }}</span>
      </div>
      <div class="progress-track" :style="trackStyle">
        <div class="progress-bar" :style="barStyle">
          <div v-if="showStripe" class="progress-stripe" :style="stripeStyle"></div>
        </div>
      </div>
    </div>
    
    <!-- 垂直进度条 -->
    <div v-else-if="config.type === 'vertical'" class="progress-vertical">
      <div class="progress-track-vertical" :style="trackStyleVertical">
        <div class="progress-bar-vertical" :style="barStyleVertical">
          <div v-if="showStripe" class="progress-stripe-vertical" :style="stripeStyleVertical"></div>
        </div>
      </div>
      <div class="progress-label-vertical" v-if="showLabel">
        <span class="label-text">{{ labelText }}</span>
        <span class="progress-value">{{ displayValue }}</span>
      </div>
    </div>
    
    <!-- 圆形进度条 -->
    <div v-else-if="config.type === 'circle'" class="progress-circle">
      <svg :width="circleSize" :height="circleSize" class="progress-svg">
        <!-- 背景圆环 -->
        <circle
          :cx="circleSize / 2"
          :cy="circleSize / 2"
          :r="radius"
          :stroke="trackColor"
          :stroke-width="strokeWidth"
          fill="none"
        />
        <!-- 进度圆环 -->
        <circle
          :cx="circleSize / 2"
          :cy="circleSize / 2"
          :r="radius"
          :stroke="barColor"
          :stroke-width="strokeWidth"
          :stroke-dasharray="circumference"
          :stroke-dashoffset="strokeDashoffset"
          :stroke-linecap="config.lineCap || 'round'"
          fill="none"
          class="progress-circle-bar"
          :style="{ transition: 'stroke-dashoffset 0.6s ease' }"
        />
      </svg>
      <div class="progress-circle-content" :style="circleContentStyle">
        <div class="progress-value-large">{{ displayValue }}</div>
        <div v-if="showLabel" class="progress-label-small">{{ labelText }}</div>
      </div>
    </div>
    
    <!-- 仪表盘样式进度条 -->
    <div v-else-if="config.type === 'dashboard'" class="progress-dashboard">
      <svg :width="circleSize" :height="circleSize * 0.75" class="progress-svg">
        <!-- 背景弧线 -->
        <path
          :d="dashboardPath"
          :stroke="trackColor"
          :stroke-width="strokeWidth"
          fill="none"
        />
        <!-- 进度弧线 -->
        <path
          :d="dashboardProgressPath"
          :stroke="barColor"
          :stroke-width="strokeWidth"
          :stroke-linecap="config.lineCap || 'round'"
          fill="none"
          class="progress-dashboard-bar"
        />
      </svg>
      <div class="progress-dashboard-content" :style="dashboardContentStyle">
        <div class="progress-value-large">{{ displayValue }}</div>
        <div v-if="showLabel" class="progress-label-small">{{ labelText }}</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  config: { type: Object, required: true },
  data: { type: Object, default: () => ({}) }
})

// 计算属性
const currentValue = computed(() => {
  return props.data.value !== undefined ? props.data.value : (props.config.value || 0)
})

const maxValue = computed(() => props.config.max || 100)
const minValue = computed(() => props.config.min || 0)

const percentage = computed(() => {
  const range = maxValue.value - minValue.value
  const value = Math.max(minValue.value, Math.min(maxValue.value, currentValue.value))
  return ((value - minValue.value) / range) * 100
})

const displayValue = computed(() => {
  const unit = props.config.unit || '%'
  const precision = props.config.precision || 0
  const showPercentage = props.config.showPercentage !== false
  
  if (showPercentage) {
    return percentage.value.toFixed(precision) + '%'
  } else {
    return currentValue.value.toFixed(precision) + unit
  }
})

const labelText = computed(() => props.config.label || props.config.name || '进度')
const showLabel = computed(() => props.config.showLabel !== false)
const showStripe = computed(() => props.config.showStripe === true)

// 样式计算
const containerStyle = computed(() => ({
  background: props.config.backgroundColor || 'transparent',
  border: props.config.border || 'none',
  borderRadius: props.config.borderRadius || '4px',
  padding: props.config.padding || '10px'
}))

const trackColor = computed(() => props.config.trackColor || 'rgba(255, 255, 255, 0.1)')
const barColor = computed(() => {
  const colors = props.config.colors || ['#00f5ff', '#1a3a8f']
  if (Array.isArray(colors) && colors.length > 1) {
    return `linear-gradient(90deg, ${colors.join(', ')})`
  }
  return colors[0] || '#00f5ff'
})

// 水平进度条样式
const trackStyle = computed(() => ({
  height: (props.config.height || 8) + 'px',
  backgroundColor: trackColor.value,
  borderRadius: (props.config.borderRadius || 4) + 'px'
}))

const barStyle = computed(() => ({
  width: percentage.value + '%',
  height: '100%',
  background: barColor.value,
  borderRadius: (props.config.borderRadius || 4) + 'px',
  transition: 'width 0.6s ease'
}))

const stripeStyle = computed(() => ({
  backgroundImage: 'linear-gradient(45deg, rgba(255,255,255,.15) 25%, transparent 25%, transparent 50%, rgba(255,255,255,.15) 50%, rgba(255,255,255,.15) 75%, transparent 75%, transparent)',
  backgroundSize: '20px 20px',
  animation: 'progress-stripe 1s linear infinite'
}))

// 垂直进度条样式
const trackStyleVertical = computed(() => ({
  width: (props.config.width || 8) + 'px',
  height: '100%',
  backgroundColor: trackColor.value,
  borderRadius: (props.config.borderRadius || 4) + 'px'
}))

const barStyleVertical = computed(() => ({
  width: '100%',
  height: percentage.value + '%',
  background: barColor.value,
  borderRadius: (props.config.borderRadius || 4) + 'px',
  transition: 'height 0.6s ease',
  marginTop: 'auto'
}))

const stripeStyleVertical = computed(() => ({
  backgroundImage: 'linear-gradient(45deg, rgba(255,255,255,.15) 25%, transparent 25%, transparent 50%, rgba(255,255,255,.15) 50%, rgba(255,255,255,.15) 75%, transparent 75%, transparent)',
  backgroundSize: '20px 20px',
  animation: 'progress-stripe 1s linear infinite'
}))

// 圆形进度条样式
const circleSize = computed(() => props.config.size || 120)
const strokeWidth = computed(() => props.config.strokeWidth || 8)
const radius = computed(() => (circleSize.value - strokeWidth.value) / 2)
const circumference = computed(() => 2 * Math.PI * radius.value)
const strokeDashoffset = computed(() => circumference.value - (percentage.value / 100) * circumference.value)

const circleContentStyle = computed(() => ({
  position: 'absolute',
  top: '50%',
  left: '50%',
  transform: 'translate(-50%, -50%)',
  textAlign: 'center'
}))

// 仪表盘样式
const dashboardPath = computed(() => {
  const centerX = circleSize.value / 2
  const centerY = circleSize.value * 0.75 - strokeWidth.value / 2
  const startAngle = Math.PI * 0.75
  const endAngle = Math.PI * 0.25
  
  const startX = centerX + radius.value * Math.cos(startAngle)
  const startY = centerY + radius.value * Math.sin(startAngle)
  const endX = centerX + radius.value * Math.cos(endAngle)
  const endY = centerY + radius.value * Math.sin(endAngle)
  
  return `M ${startX} ${startY} A ${radius.value} ${radius.value} 0 0 1 ${endX} ${endY}`
})

const dashboardProgressPath = computed(() => {
  const centerX = circleSize.value / 2
  const centerY = circleSize.value * 0.75 - strokeWidth.value / 2
  const startAngle = Math.PI * 0.75
  const progressAngle = startAngle + (Math.PI * 1.5 * percentage.value / 100)
  
  const startX = centerX + radius.value * Math.cos(startAngle)
  const startY = centerY + radius.value * Math.sin(startAngle)
  const endX = centerX + radius.value * Math.cos(progressAngle)
  const endY = centerY + radius.value * Math.sin(progressAngle)
  
  const largeArcFlag = percentage.value > 50 ? 1 : 0
  
  return `M ${startX} ${startY} A ${radius.value} ${radius.value} 0 ${largeArcFlag} 1 ${endX} ${endY}`
})

const dashboardContentStyle = computed(() => ({
  position: 'absolute',
  bottom: '20px',
  left: '50%',
  transform: 'translateX(-50%)',
  textAlign: 'center'
}))
</script>

<style scoped>
.progress-component {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  position: relative;
}

.progress-horizontal {
  width: 100%;
}

.progress-vertical {
  height: 100%;
  display: flex;
  align-items: center;
  gap: 10px;
}

.progress-label {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 14px;
  color: #ffffff;
}

.progress-label-vertical {
  display: flex;
  flex-direction: column;
  gap: 5px;
  font-size: 12px;
  color: #ffffff;
}

.progress-track {
  position: relative;
  overflow: hidden;
}

.progress-track-vertical {
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.progress-bar, .progress-bar-vertical {
  position: relative;
  overflow: hidden;
}

.progress-stripe, .progress-stripe-vertical {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.progress-circle, .progress-dashboard {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.progress-svg {
  transform: rotate(-90deg);
}

.progress-dashboard .progress-svg {
  transform: none;
}

.progress-circle-bar {
  transition: stroke-dashoffset 0.6s ease;
}

.progress-dashboard-bar {
  transition: d 0.6s ease;
}

.progress-value-large {
  font-size: 24px;
  font-weight: bold;
  color: #00f5ff;
}

.progress-label-small {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
}

@keyframes progress-stripe {
  0% {
    background-position: 0 0;
  }
  100% {
    background-position: 20px 0;
  }
}
</style>
