<template>
  <div class="gauge-component" ref="gaugeContainer"></div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import * as echarts from 'echarts'

const props = defineProps({
  config: { type: Object, required: true },
  data: { type: Object, default: () => ({}) }
})

const gaugeContainer = ref(null)
let chartInstance = null

// 仪表盘配置
const getGaugeOption = () => {
  const { config: gaugeConfig } = props.config
  const value = props.data.value || props.config.value || 0
  const max = gaugeConfig?.max || 100
  const min = gaugeConfig?.min || 0
  
  return {
    backgroundColor: 'transparent',
    series: [{
      type: 'gauge',
      min: min,
      max: max,
      radius: gaugeConfig?.radius || '80%',
      center: ['50%', '55%'],
      startAngle: gaugeConfig?.startAngle || 225,
      endAngle: gaugeConfig?.endAngle || -45,
      clockwise: gaugeConfig?.clockwise !== false,
      splitNumber: gaugeConfig?.splitNumber || 10,
      
      // 仪表盘轴线
      axisLine: {
        lineStyle: {
          width: gaugeConfig?.axisLineWidth || 10,
          color: [
            [0.3, '#FF6E76'],
            [0.7, '#FDDD60'],
            [1, '#58D9F9']
          ]
        }
      },
      
      // 分隔线
      splitLine: {
        distance: -gaugeConfig?.axisLineWidth || -10,
        length: gaugeConfig?.splitLineLength || 14,
        lineStyle: {
          width: 2,
          color: '#999'
        }
      },
      
      // 刻度标签
      axisLabel: {
        distance: -gaugeConfig?.labelDistance || -40,
        color: '#999',
        fontSize: gaugeConfig?.labelFontSize || 12
      },
      
      // 刻度
      axisTick: {
        distance: -gaugeConfig?.tickDistance || -12,
        length: gaugeConfig?.tickLength || 8,
        lineStyle: {
          color: '#999',
          width: 1
        }
      },
      
      // 指针
      pointer: {
        icon: 'path://M12.8,0.7l12,40.1H0.7L12.8,0.7z',
        length: gaugeConfig?.pointerLength || '75%',
        width: gaugeConfig?.pointerWidth || 6,
        offsetCenter: [0, '-60%'],
        itemStyle: {
          color: gaugeConfig?.pointerColor || '#00f5ff'
        }
      },
      
      // 标题
      title: {
        offsetCenter: [0, '20%'],
        fontSize: gaugeConfig?.titleFontSize || 16,
        color: gaugeConfig?.titleColor || '#ffffff'
      },
      
      // 数值显示
      detail: {
        fontSize: gaugeConfig?.valueFontSize || 24,
        offsetCenter: [0, '40%'],
        valueAnimation: true,
        formatter: function(value) {
          const unit = gaugeConfig?.unit || ''
          const precision = gaugeConfig?.precision || 0
          return value.toFixed(precision) + unit
        },
        color: gaugeConfig?.valueColor || '#00f5ff'
      },
      
      data: [{
        value: value,
        name: gaugeConfig?.title || props.config.name || '仪表盘'
      }]
    }],
    
    // 动画配置
    animation: gaugeConfig?.animation !== false,
    animationDuration: gaugeConfig?.animationDuration || 2000,
    animationEasing: gaugeConfig?.animationEasing || 'elasticOut'
  }
}

// 初始化图表
const initChart = () => {
  if (!gaugeContainer.value) return
  
  chartInstance = echarts.init(gaugeContainer.value)
  updateChart()
}

// 更新图表
const updateChart = () => {
  if (!chartInstance) return
  
  const option = getGaugeOption()
  chartInstance.setOption(option, true)
}

// 调整图表大小
const resizeChart = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

// 监听配置变化
watch(() => props.config, () => {
  updateChart()
}, { deep: true })

watch(() => props.data, () => {
  updateChart()
}, { deep: true })

// 生命周期
onMounted(() => {
  nextTick(() => {
    initChart()
  })
  
  // 监听窗口大小变化
  window.addEventListener('resize', resizeChart)
})

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
    chartInstance = null
  }
  window.removeEventListener('resize', resizeChart)
})

// 暴露方法给父组件
defineExpose({
  resizeChart,
  updateChart
})
</script>

<style scoped>
.gauge-component {
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}
</style>
