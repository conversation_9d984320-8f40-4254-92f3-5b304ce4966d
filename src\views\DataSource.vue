<template>
  <div class="datasource-container">
    <!-- 顶部导航 -->
    <header class="header">
      <div class="header-content">
        <div class="logo" @click="goHome">
          <img src="/favicon.ico" alt="DataV" class="logo-icon">
          <span class="logo-text">DataV</span>
        </div>
        <div class="header-actions">
          <el-button type="primary" @click="createDataSource">
            <el-icon><Plus /></el-icon>
            新建数据源
          </el-button>
        </div>
      </div>
    </header>

    <!-- 主要内容 -->
    <main class="main-content">
      <!-- 工具栏 -->
      <div class="toolbar">
        <div class="toolbar-left">
          <h1 class="page-title">数据源管理</h1>
          <span class="datasource-count">共 {{ dataSourceList.length }} 个数据源</span>
        </div>
        <div class="toolbar-right">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索数据源..."
            style="width: 300px"
            @input="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
          <el-select v-model="typeFilter" placeholder="类型筛选" style="width: 120px" @change="handleFilter">
            <el-option label="全部" value="" />
            <el-option label="API" value="api" />
            <el-option label="数据库" value="database" />
            <el-option label="静态数据" value="static" />
            <el-option label="文件" value="file" />
          </el-select>
          <el-select v-model="statusFilter" placeholder="状态筛选" style="width: 120px" @change="handleFilter">
            <el-option label="全部" value="" />
            <el-option label="正常" value="active" />
            <el-option label="异常" value="error" />
            <el-option label="未测试" value="untested" />
          </el-select>
        </div>
      </div>

      <!-- 数据源列表 -->
      <div class="datasource-grid" v-loading="loading">
        <div
          v-for="dataSource in filteredDataSources"
          :key="dataSource.id"
          class="datasource-card"
          @click="editDataSource(dataSource)"
        >
          <div class="card-header">
            <div class="datasource-icon">
              <el-icon size="24">
                <component :is="getDataSourceIcon(dataSource.type)" />
              </el-icon>
            </div>
            <div class="datasource-status" :class="dataSource.status">
              <el-icon size="12">
                <component :is="getStatusIcon(dataSource.status)" />
              </el-icon>
            </div>
          </div>
          
          <div class="card-content">
            <h3 class="datasource-name">{{ dataSource.name }}</h3>
            <p class="datasource-type">{{ getDataSourceTypeName(dataSource.type) }}</p>
            <p class="datasource-desc">{{ dataSource.description || '暂无描述' }}</p>
            
            <div class="datasource-meta">
              <span class="meta-item">
                <el-icon><Clock /></el-icon>
                {{ formatTime(dataSource.updateTime) }}
              </span>
              <span class="meta-item" v-if="dataSource.updateInterval">
                <el-icon><Refresh /></el-icon>
                {{ dataSource.updateInterval / 1000 }}s
              </span>
            </div>
          </div>
          
          <div class="card-actions">
            <el-button size="small" @click.stop="testConnection(dataSource)">
              <el-icon><Connection /></el-icon>
              测试
            </el-button>
            <el-button size="small" @click.stop="previewData(dataSource)">
              <el-icon><View /></el-icon>
              预览
            </el-button>
            <el-dropdown @command="handleCommand" trigger="click" @click.stop>
              <el-button size="small">
                <el-icon><More /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item :command="{action: 'edit', id: dataSource.id}">
                    <el-icon><Edit /></el-icon>
                    编辑
                  </el-dropdown-item>
                  <el-dropdown-item :command="{action: 'copy', id: dataSource.id}">
                    <el-icon><CopyDocument /></el-icon>
                    复制
                  </el-dropdown-item>
                  <el-dropdown-item :command="{action: 'export', id: dataSource.id}">
                    <el-icon><Download /></el-icon>
                    导出
                  </el-dropdown-item>
                  <el-dropdown-item :command="{action: 'delete', id: dataSource.id}" divided>
                    <el-icon><Delete /></el-icon>
                    删除
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-if="filteredDataSources.length === 0 && !loading" class="empty-state">
          <el-icon size="80"><DataBoard /></el-icon>
          <h3>暂无数据源</h3>
          <p>点击"新建数据源"开始创建您的第一个数据源</p>
          <el-button type="primary" @click="createDataSource">
            <el-icon><Plus /></el-icon>
            新建数据源
          </el-button>
        </div>
      </div>
    </main>

    <!-- 数据源编辑对话框 -->
    <DataSourceDialog
      v-model="dialogVisible"
      :datasource="currentDataSource"
      @save="handleSaveDataSource"
    />

    <!-- 数据预览对话框 -->
    <DataPreviewDialog
      v-model="previewDialogVisible"
      :datasource="previewDataSource"
    />
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getDataSourceList, deleteDataSource, testDataSource } from '@/api/dataSourceApi'
import DataSourceDialog from '@/components/datasource/DataSourceDialog.vue'
import DataPreviewDialog from '@/components/datasource/DataPreviewDialog.vue'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const dataSourceList = ref([])
const searchKeyword = ref('')
const typeFilter = ref('')
const statusFilter = ref('')
const dialogVisible = ref(false)
const previewDialogVisible = ref(false)
const currentDataSource = ref(null)
const previewDataSource = ref(null)

// 计算属性
const filteredDataSources = computed(() => {
  let sources = [...dataSourceList.value]
  
  // 搜索过滤
  if (searchKeyword.value) {
    sources = sources.filter(source => 
      source.name.includes(searchKeyword.value) || 
      (source.description && source.description.includes(searchKeyword.value))
    )
  }
  
  // 类型过滤
  if (typeFilter.value) {
    sources = sources.filter(source => source.type === typeFilter.value)
  }
  
  // 状态过滤
  if (statusFilter.value) {
    sources = sources.filter(source => source.status === statusFilter.value)
  }
  
  return sources
})

// 方法
const loadDataSources = async () => {
  loading.value = true
  try {
    const res = await getDataSourceList()
    if (res.code === 200) {
      dataSourceList.value = res.data.list
    }
  } catch (error) {
    ElMessage.error('加载数据源列表失败')
  } finally {
    loading.value = false
  }
}

const goHome = () => {
  router.push('/')
}

const createDataSource = () => {
  currentDataSource.value = null
  dialogVisible.value = true
}

const editDataSource = (dataSource) => {
  currentDataSource.value = dataSource
  dialogVisible.value = true
}

const testConnection = async (dataSource) => {
  try {
    ElMessage.info('正在测试连接...')
    const res = await testDataSource(dataSource)
    if (res.code === 200) {
      ElMessage.success('连接测试成功')
      // 更新状态
      const index = dataSourceList.value.findIndex(ds => ds.id === dataSource.id)
      if (index !== -1) {
        dataSourceList.value[index].status = 'active'
      }
    } else {
      ElMessage.error('连接测试失败: ' + res.msg)
      // 更新状态
      const index = dataSourceList.value.findIndex(ds => ds.id === dataSource.id)
      if (index !== -1) {
        dataSourceList.value[index].status = 'error'
      }
    }
  } catch (error) {
    ElMessage.error('连接测试失败')
  }
}

const previewData = (dataSource) => {
  previewDataSource.value = dataSource
  previewDialogVisible.value = true
}

const handleCommand = async (command) => {
  const { action, id } = command
  
  switch (action) {
    case 'edit':
      const dataSource = dataSourceList.value.find(ds => ds.id === id)
      editDataSource(dataSource)
      break
      
    case 'copy':
      // 复制数据源逻辑
      ElMessage.success('数据源复制成功')
      break
      
    case 'export':
      // 导出数据源逻辑
      ElMessage.success('数据源导出成功')
      break
      
    case 'delete':
      ElMessageBox.confirm('确定要删除这个数据源吗？', '确认删除', {
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          const res = await deleteDataSource(id)
          if (res.code === 200) {
            ElMessage.success('数据源删除成功')
            loadDataSources()
          }
        } catch (error) {
          ElMessage.error('删除数据源失败')
        }
      })
      break
  }
}

const handleSaveDataSource = () => {
  loadDataSources()
}

const handleSearch = () => {
  // 搜索逻辑已在计算属性中处理
}

const handleFilter = () => {
  // 过滤逻辑已在计算属性中处理
}

const getDataSourceIcon = (type) => {
  const iconMap = {
    api: 'Link',
    database: 'Coin',
    static: 'Document',
    file: 'Folder'
  }
  return iconMap[type] || 'DataBoard'
}

const getDataSourceTypeName = (type) => {
  const nameMap = {
    api: 'API接口',
    database: '数据库',
    static: '静态数据',
    file: '文件数据'
  }
  return nameMap[type] || type
}

const getStatusIcon = (status) => {
  const iconMap = {
    active: 'SuccessFilled',
    error: 'CircleCloseFilled',
    untested: 'QuestionFilled'
  }
  return iconMap[status] || 'QuestionFilled'
}

const formatTime = (time) => {
  return new Date(time).toLocaleString()
}

// 生命周期
onMounted(() => {
  loadDataSources()
})
</script>

<style scoped>
.datasource-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #0a1a3a 0%, #1a3a8f 100%);
  color: #ffffff;
}

.header {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding: 0 2rem;
}

.header-content {
  max-width: 1400px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 70px;
}

.logo {
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
}

.logo-icon {
  width: 32px;
  height: 32px;
}

.logo-text {
  font-size: 20px;
  font-weight: 600;
  color: #ffffff;
}

.main-content {
  max-width: 1400px;
  margin: 0 auto;
  padding: 2rem;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.page-title {
  font-size: 2rem;
  font-weight: 600;
  margin: 0;
}

.datasource-count {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.9rem;
}

.toolbar-right {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.datasource-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 2rem;
}

.datasource-card {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  padding: 1.5rem;
  position: relative;
}

.datasource-card:hover {
  transform: translateY(-5px);
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(0, 245, 255, 0.3);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.datasource-icon {
  color: #00f5ff;
}

.datasource-status {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.datasource-status.active {
  color: #00ff88;
}

.datasource-status.error {
  color: #ff4757;
}

.datasource-status.untested {
  color: #ffa502;
}

.card-content {
  margin-bottom: 1rem;
}

.datasource-name {
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
  color: #ffffff;
}

.datasource-type {
  color: #00f5ff;
  margin: 0 0 0.5rem 0;
  font-size: 0.9rem;
}

.datasource-desc {
  color: rgba(255, 255, 255, 0.7);
  margin: 0 0 1rem 0;
  font-size: 0.9rem;
  line-height: 1.4;
}

.datasource-meta {
  display: flex;
  gap: 1rem;
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.5);
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.card-actions {
  display: flex;
  gap: 0.5rem;
}

.empty-state {
  grid-column: 1 / -1;
  text-align: center;
  padding: 4rem 2rem;
  color: rgba(255, 255, 255, 0.7);
}

.empty-state h3 {
  margin: 1rem 0;
  color: #ffffff;
}

@media (max-width: 768px) {
  .toolbar {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }
  
  .toolbar-right {
    flex-direction: column;
  }
  
  .datasource-grid {
    grid-template-columns: 1fr;
  }
}
</style>
