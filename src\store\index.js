/**
 * 简单的状态管理
 * 使用Vue 3的reactive实现轻量级状态管理
 */

import { reactive, readonly } from 'vue'

// 全局状态
const state = reactive({
  // 用户信息
  user: {
    id: null,
    name: '游客',
    avatar: '',
    permissions: []
  },

  // 当前项目信息
  currentProject: {
    id: null,
    name: '',
    config: {},
    components: [],
    isDirty: false // 是否有未保存的更改
  },

  // 编辑器状态
  editor: {
    selectedComponent: null,
    clipboard: null,
    history: [],
    historyIndex: -1,
    zoom: 100,
    showGrid: true,
    showRuler: true,
    canvasSize: {
      width: 1920,
      height: 1080
    }
  },

  // 组件库
  componentLibrary: {
    categories: [],
    components: [],
    loading: false
  },

  // 数据源
  dataSources: {
    list: [],
    loading: false
  },

  // 应用设置
  settings: {
    theme: 'dark',
    language: 'zh-CN',
    autoSave: true,
    autoSaveInterval: 30000 // 30秒
  }
})

// 状态操作方法
const actions = {
  // 用户相关
  setUser(userInfo) {
    Object.assign(state.user, userInfo)
  },

  clearUser() {
    state.user = {
      id: null,
      name: '游客',
      avatar: '',
      permissions: []
    }
  },

  // 项目相关
  setCurrentProject(project) {
    Object.assign(state.currentProject, project)
    state.currentProject.isDirty = false
  },

  updateProjectConfig(config) {
    Object.assign(state.currentProject.config, config)
    state.currentProject.isDirty = true
  },

  addComponent(component) {
    state.currentProject.components.push(component)
    state.currentProject.isDirty = true
    actions.saveHistory()
  },

  updateComponent(componentId, updates) {
    const index = state.currentProject.components.findIndex(c => c.id === componentId)
    if (index !== -1) {
      Object.assign(state.currentProject.components[index], updates)
      state.currentProject.isDirty = true
    }
  },

  removeComponent(componentId) {
    const index = state.currentProject.components.findIndex(c => c.id === componentId)
    if (index !== -1) {
      state.currentProject.components.splice(index, 1)
      state.currentProject.isDirty = true
      actions.saveHistory()
    }
  },

  clearProject() {
    state.currentProject = {
      id: null,
      name: '',
      config: {},
      components: [],
      isDirty: false
    }
    state.editor.selectedComponent = null
    state.editor.history = []
    state.editor.historyIndex = -1
  },

  // 编辑器相关
  setSelectedComponent(component) {
    state.editor.selectedComponent = component
  },

  setZoom(zoom) {
    state.editor.zoom = Math.max(10, Math.min(500, zoom))
  },

  toggleGrid() {
    state.editor.showGrid = !state.editor.showGrid
  },

  toggleRuler() {
    state.editor.showRuler = !state.editor.showRuler
  },

  setCanvasSize(width, height) {
    state.editor.canvasSize.width = width
    state.editor.canvasSize.height = height
    state.currentProject.isDirty = true
  },

  // 历史记录相关
  saveHistory() {
    const snapshot = JSON.parse(JSON.stringify(state.currentProject.components))
    
    // 如果当前不在历史记录的末尾，删除后面的记录
    if (state.editor.historyIndex < state.editor.history.length - 1) {
      state.editor.history.splice(state.editor.historyIndex + 1)
    }
    
    state.editor.history.push(snapshot)
    state.editor.historyIndex = state.editor.history.length - 1
    
    // 限制历史记录数量
    if (state.editor.history.length > 50) {
      state.editor.history.shift()
      state.editor.historyIndex--
    }
  },

  undo() {
    if (state.editor.historyIndex > 0) {
      state.editor.historyIndex--
      const snapshot = state.editor.history[state.editor.historyIndex]
      state.currentProject.components = JSON.parse(JSON.stringify(snapshot))
      state.currentProject.isDirty = true
    }
  },

  redo() {
    if (state.editor.historyIndex < state.editor.history.length - 1) {
      state.editor.historyIndex++
      const snapshot = state.editor.history[state.editor.historyIndex]
      state.currentProject.components = JSON.parse(JSON.stringify(snapshot))
      state.currentProject.isDirty = true
    }
  },

  // 剪贴板相关
  copyComponent(component) {
    state.editor.clipboard = JSON.parse(JSON.stringify(component))
  },

  pasteComponent() {
    if (state.editor.clipboard) {
      const newComponent = JSON.parse(JSON.stringify(state.editor.clipboard))
      newComponent.id = Date.now()
      newComponent.x += 20
      newComponent.y += 20
      actions.addComponent(newComponent)
      return newComponent
    }
    return null
  },

  // 组件库相关
  setComponentCategories(categories) {
    state.componentLibrary.categories = categories
  },

  setComponents(components) {
    state.componentLibrary.components = components
  },

  setComponentLibraryLoading(loading) {
    state.componentLibrary.loading = loading
  },

  // 数据源相关
  setDataSources(dataSources) {
    state.dataSources.list = dataSources
  },

  setDataSourcesLoading(loading) {
    state.dataSources.loading = loading
  },

  // 设置相关
  updateSettings(settings) {
    Object.assign(state.settings, settings)
    // 保存到本地存储
    localStorage.setItem('datav-settings', JSON.stringify(state.settings))
  },

  loadSettings() {
    try {
      const saved = localStorage.getItem('datav-settings')
      if (saved) {
        Object.assign(state.settings, JSON.parse(saved))
      }
    } catch (error) {
      console.error('Failed to load settings:', error)
    }
  }
}

// 计算属性
const getters = {
  // 是否可以撤销
  canUndo: () => state.editor.historyIndex > 0,
  
  // 是否可以重做
  canRedo: () => state.editor.historyIndex < state.editor.history.length - 1,
  
  // 是否有剪贴板内容
  hasClipboard: () => !!state.editor.clipboard,
  
  // 获取选中的组件
  selectedComponent: () => state.editor.selectedComponent,
  
  // 获取当前项目的组件数量
  componentCount: () => state.currentProject.components.length,
  
  // 是否有未保存的更改
  isDirty: () => state.currentProject.isDirty
}

// 导出只读状态和操作方法
export const useStore = () => ({
  state: readonly(state),
  actions,
  getters
})

// 初始化
actions.loadSettings()

export default useStore
