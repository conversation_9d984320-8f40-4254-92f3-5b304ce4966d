<template>
  <el-dialog
    v-model="visible"
    title="数据预览"
    width="1000px"
    :before-close="handleClose"
  >
    <div class="preview-container">
      <!-- 工具栏 -->
      <div class="preview-toolbar">
        <div class="toolbar-left">
          <el-button @click="refreshData" :loading="loading" size="small">
            <el-icon><Refresh /></el-icon>
            刷新数据
          </el-button>
          <el-button @click="exportData" size="small">
            <el-icon><Download /></el-icon>
            导出数据
          </el-button>
        </div>
        <div class="toolbar-right">
          <el-radio-group v-model="viewMode" size="small">
            <el-radio-button label="table">表格视图</el-radio-button>
            <el-radio-button label="json">JSON视图</el-radio-button>
            <el-radio-button label="chart">图表视图</el-radio-button>
          </el-radio-group>
        </div>
      </div>

      <!-- 数据信息 -->
      <div class="data-info">
        <div class="info-item">
          <span class="label">数据源：</span>
          <span class="value">{{ datasource?.name }}</span>
        </div>
        <div class="info-item">
          <span class="label">类型：</span>
          <span class="value">{{ getDataSourceTypeName(datasource?.type) }}</span>
        </div>
        <div class="info-item">
          <span class="label">记录数：</span>
          <span class="value">{{ previewData.length }}</span>
        </div>
        <div class="info-item">
          <span class="label">更新时间：</span>
          <span class="value">{{ formatTime(updateTime) }}</span>
        </div>
      </div>

      <!-- 数据内容 -->
      <div class="preview-content" v-loading="loading">
        <!-- 表格视图 -->
        <div v-if="viewMode === 'table'" class="table-view">
          <el-table
            :data="paginatedData"
            style="width: 100%"
            max-height="400"
            stripe
            border
          >
            <el-table-column
              v-for="column in tableColumns"
              :key="column.prop"
              :prop="column.prop"
              :label="column.label"
              :width="column.width"
              show-overflow-tooltip
            >
              <template #default="{ row }">
                <span :class="getValueClass(row[column.prop])">
                  {{ formatValue(row[column.prop]) }}
                </span>
              </template>
            </el-table-column>
          </el-table>
          
          <!-- 分页 -->
          <el-pagination
            v-if="previewData.length > pageSize"
            v-model:current-page="currentPage"
            :page-size="pageSize"
            :total="previewData.length"
            layout="total, prev, pager, next"
            style="margin-top: 20px; justify-content: center;"
          />
        </div>

        <!-- JSON视图 -->
        <div v-if="viewMode === 'json'" class="json-view">
          <pre class="json-content">{{ formattedJSON }}</pre>
        </div>

        <!-- 图表视图 -->
        <div v-if="viewMode === 'chart'" class="chart-view">
          <div class="chart-controls">
            <el-form inline>
              <el-form-item label="X轴字段:">
                <el-select v-model="chartConfig.xField" placeholder="选择X轴字段" style="width: 150px">
                  <el-option
                    v-for="field in numericFields"
                    :key="field"
                    :label="field"
                    :value="field"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="Y轴字段:">
                <el-select v-model="chartConfig.yField" placeholder="选择Y轴字段" style="width: 150px">
                  <el-option
                    v-for="field in numericFields"
                    :key="field"
                    :label="field"
                    :value="field"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="图表类型:">
                <el-select v-model="chartConfig.type" style="width: 120px">
                  <el-option label="柱状图" value="bar" />
                  <el-option label="折线图" value="line" />
                  <el-option label="散点图" value="scatter" />
                </el-select>
              </el-form-item>
            </el-form>
          </div>
          <div ref="chartContainer" class="chart-container"></div>
        </div>

        <!-- 空数据状态 -->
        <div v-if="previewData.length === 0 && !loading" class="empty-state">
          <el-icon size="60"><DataBoard /></el-icon>
          <p>暂无数据</p>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { getDataSourceData } from '@/api/dataSourceApi'
import * as echarts from 'echarts'

const props = defineProps({
  modelValue: { type: Boolean, default: false },
  datasource: { type: Object, default: null }
})

const emit = defineEmits(['update:modelValue'])

// 响应式数据
const loading = ref(false)
const viewMode = ref('table')
const previewData = ref([])
const updateTime = ref(new Date())
const currentPage = ref(1)
const pageSize = ref(20)
const chartContainer = ref(null)
let chartInstance = null

const chartConfig = reactive({
  xField: '',
  yField: '',
  type: 'bar'
})

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const tableColumns = computed(() => {
  if (previewData.value.length === 0) return []
  
  const firstRow = previewData.value[0]
  return Object.keys(firstRow).map(key => ({
    prop: key,
    label: key,
    width: getColumnWidth(key, firstRow[key])
  }))
})

const numericFields = computed(() => {
  if (previewData.value.length === 0) return []
  
  const firstRow = previewData.value[0]
  return Object.keys(firstRow).filter(key => {
    const value = firstRow[key]
    return !isNaN(value) && value !== null && value !== ''
  })
})

const paginatedData = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return previewData.value.slice(start, end)
})

const formattedJSON = computed(() => {
  return JSON.stringify(previewData.value, null, 2)
})

// 监听数据源变化
watch(() => props.datasource, (newDataSource) => {
  if (newDataSource && visible.value) {
    loadData()
  }
}, { immediate: true })

// 监听对话框显示
watch(visible, (show) => {
  if (show && props.datasource) {
    loadData()
  }
})

// 监听图表配置变化
watch([() => chartConfig.xField, () => chartConfig.yField, () => chartConfig.type], () => {
  if (viewMode.value === 'chart') {
    nextTick(() => {
      renderChart()
    })
  }
})

// 监听视图模式变化
watch(viewMode, (mode) => {
  if (mode === 'chart') {
    nextTick(() => {
      initChart()
    })
  }
})

// 方法
const loadData = async () => {
  if (!props.datasource) return
  
  loading.value = true
  try {
    const res = await getDataSourceData(props.datasource.id)
    if (res.code === 200) {
      previewData.value = res.data.data || []
      updateTime.value = new Date()
      
      // 自动设置图表字段
      if (numericFields.value.length > 0) {
        chartConfig.xField = numericFields.value[0]
        chartConfig.yField = numericFields.value[1] || numericFields.value[0]
      }
    } else {
      ElMessage.error('加载数据失败: ' + res.msg)
      previewData.value = []
    }
  } catch (error) {
    ElMessage.error('加载数据失败')
    previewData.value = []
  } finally {
    loading.value = false
  }
}

const refreshData = () => {
  loadData()
}

const exportData = () => {
  const dataStr = JSON.stringify(previewData.value, null, 2)
  const blob = new Blob([dataStr], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = `${props.datasource?.name || 'data'}.json`
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  URL.revokeObjectURL(url)
  ElMessage.success('数据导出成功')
}

const getDataSourceTypeName = (type) => {
  const nameMap = {
    api: 'API接口',
    mysql: 'MySQL数据库',
    postgresql: 'PostgreSQL数据库',
    mongodb: 'MongoDB数据库',
    static: '静态数据',
    csv: 'CSV文件',
    json: 'JSON文件'
  }
  return nameMap[type] || type
}

const formatTime = (time) => {
  return new Date(time).toLocaleString()
}

const getColumnWidth = (key, value) => {
  const keyLength = key.length
  const valueLength = String(value).length
  const maxLength = Math.max(keyLength, valueLength)
  return Math.min(Math.max(maxLength * 8 + 40, 100), 200)
}

const formatValue = (value) => {
  if (value === null || value === undefined) return '-'
  if (typeof value === 'object') return JSON.stringify(value)
  return String(value)
}

const getValueClass = (value) => {
  if (value === null || value === undefined) return 'null-value'
  if (typeof value === 'number') return 'number-value'
  if (typeof value === 'boolean') return 'boolean-value'
  return 'string-value'
}

const initChart = () => {
  if (!chartContainer.value) return
  
  if (chartInstance) {
    chartInstance.dispose()
  }
  
  chartInstance = echarts.init(chartContainer.value)
  renderChart()
}

const renderChart = () => {
  if (!chartInstance || !chartConfig.xField || !chartConfig.yField) return
  
  const chartData = previewData.value.map(item => ({
    x: item[chartConfig.xField],
    y: item[chartConfig.yField]
  })).filter(item => !isNaN(item.x) && !isNaN(item.y))
  
  const option = {
    backgroundColor: 'transparent',
    title: {
      text: '数据预览图表',
      textStyle: { color: '#333' }
    },
    tooltip: {
      trigger: 'item'
    },
    xAxis: {
      type: chartConfig.type === 'scatter' ? 'value' : 'category',
      data: chartConfig.type === 'scatter' ? undefined : chartData.map(item => item.x),
      name: chartConfig.xField
    },
    yAxis: {
      type: 'value',
      name: chartConfig.yField
    },
    series: [{
      type: chartConfig.type,
      data: chartConfig.type === 'scatter' 
        ? chartData.map(item => [item.x, item.y])
        : chartData.map(item => item.y)
    }]
  }
  
  chartInstance.setOption(option, true)
}

const handleClose = () => {
  visible.value = false
  if (chartInstance) {
    chartInstance.dispose()
    chartInstance = null
  }
}
</script>

<style scoped>
.preview-container {
  height: 600px;
  display: flex;
  flex-direction: column;
}

.preview-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.toolbar-left {
  display: flex;
  gap: 10px;
}

.data-info {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  padding: 10px;
  background: #f5f5f5;
  border-radius: 4px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 5px;
}

.label {
  font-weight: 500;
  color: #666;
}

.value {
  color: #333;
}

.preview-content {
  flex: 1;
  overflow: hidden;
}

.table-view {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.json-view {
  height: 100%;
  overflow: auto;
}

.json-content {
  background: #f8f8f8;
  padding: 20px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.5;
  margin: 0;
  white-space: pre-wrap;
  word-break: break-all;
}

.chart-view {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.chart-controls {
  margin-bottom: 20px;
  padding: 10px;
  background: #f5f5f5;
  border-radius: 4px;
}

.chart-container {
  flex: 1;
  min-height: 300px;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #999;
}

.empty-state p {
  margin-top: 10px;
  font-size: 14px;
}

.null-value {
  color: #999;
  font-style: italic;
}

.number-value {
  color: #1890ff;
  font-weight: 500;
}

.boolean-value {
  color: #52c41a;
  font-weight: 500;
}

.string-value {
  color: #333;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}
</style>
