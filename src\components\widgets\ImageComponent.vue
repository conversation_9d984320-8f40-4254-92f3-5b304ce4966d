<template>
  <div class="image-component" :style="containerStyle">
    <img
      v-if="imageSrc"
      :src="imageSrc"
      :alt="config.alt || '图片'"
      :style="imageStyle"
      @load="handleImageLoad"
      @error="handleImageError"
    />
    <div v-else class="image-placeholder">
      <el-icon size="40"><Picture /></el-icon>
      <p>暂无图片</p>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'

const props = defineProps({
  config: { type: Object, required: true },
  data: { type: Object, default: () => ({}) }
})

const imageLoaded = ref(false)
const imageError = ref(false)

const imageSrc = computed(() => {
  return props.data.src || props.config.src || ''
})

const containerStyle = computed(() => ({
  width: '100%',
  height: '100%',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  background: props.config.backgroundColor || 'transparent',
  border: props.config.border || 'none',
  borderRadius: props.config.borderRadius || '0px',
  overflow: 'hidden'
}))

const imageStyle = computed(() => ({
  maxWidth: '100%',
  maxHeight: '100%',
  objectFit: props.config.objectFit || 'contain',
  opacity: imageLoaded.value ? 1 : 0,
  transition: 'opacity 0.3s ease'
}))

const handleImageLoad = () => {
  imageLoaded.value = true
  imageError.value = false
}

const handleImageError = () => {
  imageLoaded.value = false
  imageError.value = true
}
</script>

<style scoped>
.image-component {
  user-select: none;
}

.image-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: rgba(255, 255, 255, 0.5);
  text-align: center;
}

.image-placeholder p {
  margin-top: 10px;
  font-size: 14px;
}
</style>
