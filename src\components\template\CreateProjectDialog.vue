<template>
  <el-dialog
    v-model="visible"
    title="创建项目"
    width="600px"
    :before-close="handleClose"
  >
    <div class="create-project-container">
      <!-- 模板信息 -->
      <div v-if="template" class="template-preview">
        <div class="preview-image">
          <img :src="template.thumbnail" :alt="template.name" />
        </div>
        <div class="template-info">
          <h3>{{ template.name }}</h3>
          <p>{{ template.description }}</p>
          <div class="template-meta">
            <span class="category">{{ template.categoryName }}</span>
            <span class="size">{{ template.width }}×{{ template.height }}</span>
          </div>
        </div>
      </div>

      <!-- 项目配置表单 -->
      <el-form :model="formData" :rules="rules" ref="formRef" label-width="100px">
        <el-form-item label="项目名称" prop="name">
          <el-input
            v-model="formData.name"
            placeholder="请输入项目名称"
            maxlength="50"
            show-word-limit
          />
        </el-form-item>
        
        <el-form-item label="项目描述" prop="description">
          <el-input
            v-model="formData.description"
            type="textarea"
            :rows="3"
            placeholder="请输入项目描述（可选）"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>
        
        <el-form-item label="画布尺寸">
          <div class="canvas-size-options">
            <el-radio-group v-model="selectedSize" @change="handleSizeChange">
              <el-radio-button label="1920x1080">1920×1080 (推荐)</el-radio-button>
              <el-radio-button label="1366x768">1366×768</el-radio-button>
              <el-radio-button label="custom">自定义</el-radio-button>
            </el-radio-group>
          </div>
          <div v-if="selectedSize === 'custom'" class="custom-size">
            <div class="size-inputs">
              <el-input-number
                v-model="formData.width"
                :min="800"
                :max="4000"
                :step="1"
                placeholder="宽度"
                style="width: 120px"
              />
              <span class="size-separator">×</span>
              <el-input-number
                v-model="formData.height"
                :min="600"
                :max="3000"
                :step="1"
                placeholder="高度"
                style="width: 120px"
              />
              <span class="size-unit">像素</span>
            </div>
          </div>
        </el-form-item>
        
        <el-form-item label="项目分组">
          <el-select
            v-model="formData.groupId"
            placeholder="选择项目分组（可选）"
            style="width: 100%"
            clearable
          >
            <el-option
              v-for="group in projectGroups"
              :key="group.id"
              :label="group.name"
              :value="group.id"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="访问权限">
          <el-radio-group v-model="formData.visibility">
            <el-radio label="private">
              <div class="permission-option">
                <div class="option-title">私有</div>
                <div class="option-desc">只有您可以查看和编辑</div>
              </div>
            </el-radio>
            <el-radio label="team">
              <div class="permission-option">
                <div class="option-title">团队</div>
                <div class="option-desc">团队成员可以查看和编辑</div>
              </div>
            </el-radio>
            <el-radio label="public">
              <div class="permission-option">
                <div class="option-title">公开</div>
                <div class="option-desc">所有人都可以查看</div>
              </div>
            </el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="高级选项">
          <el-checkbox v-model="formData.enableAutoSave">启用自动保存</el-checkbox>
          <el-checkbox v-model="formData.enableVersionControl">启用版本控制</el-checkbox>
        </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleCreate" :loading="creating">
          创建项目
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { createProjectFromTemplate } from '@/api/templateApi'

const props = defineProps({
  modelValue: { type: Boolean, default: false },
  template: { type: Object, default: null }
})

const emit = defineEmits(['update:modelValue', 'created'])

// 响应式数据
const formRef = ref(null)
const creating = ref(false)
const selectedSize = ref('1920x1080')

const formData = reactive({
  name: '',
  description: '',
  width: 1920,
  height: 1080,
  groupId: null,
  visibility: 'private',
  enableAutoSave: true,
  enableVersionControl: false
})

const projectGroups = ref([
  { id: 1, name: '默认分组' },
  { id: 2, name: '销售报表' },
  { id: 3, name: '运营监控' },
  { id: 4, name: '财务分析' }
])

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入项目名称', trigger: 'blur' },
    { min: 2, max: 50, message: '项目名称长度在 2 到 50 个字符', trigger: 'blur' }
  ]
}

// 监听模板变化
watch(() => props.template, (newTemplate) => {
  if (newTemplate) {
    formData.name = newTemplate.name + ' - 副本'
    formData.description = newTemplate.description
    formData.width = newTemplate.width
    formData.height = newTemplate.height
    
    // 根据模板尺寸设置选中的尺寸选项
    if (newTemplate.width === 1920 && newTemplate.height === 1080) {
      selectedSize.value = '1920x1080'
    } else if (newTemplate.width === 1366 && newTemplate.height === 768) {
      selectedSize.value = '1366x768'
    } else {
      selectedSize.value = 'custom'
    }
  }
}, { immediate: true })

// 方法
const handleSizeChange = (size) => {
  switch (size) {
    case '1920x1080':
      formData.width = 1920
      formData.height = 1080
      break
    case '1366x768':
      formData.width = 1366
      formData.height = 768
      break
    case 'custom':
      // 保持当前尺寸
      break
  }
}

const handleCreate = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    creating.value = true
    
    const projectData = {
      name: formData.name,
      description: formData.description,
      width: formData.width,
      height: formData.height,
      groupId: formData.groupId,
      visibility: formData.visibility,
      settings: {
        autoSave: formData.enableAutoSave,
        versionControl: formData.enableVersionControl
      }
    }
    
    const res = await createProjectFromTemplate(props.template.id, projectData)
    
    if (res.code === 200) {
      ElMessage.success('项目创建成功')
      emit('created', res.data)
      handleClose()
    } else {
      ElMessage.error(res.msg || '创建项目失败')
    }
  } catch (error) {
    if (error !== false) { // 表单验证失败时会返回false
      ElMessage.error('创建项目失败')
    }
  } finally {
    creating.value = false
  }
}

const handleClose = () => {
  visible.value = false
  resetForm()
}

const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  Object.assign(formData, {
    name: '',
    description: '',
    width: 1920,
    height: 1080,
    groupId: null,
    visibility: 'private',
    enableAutoSave: true,
    enableVersionControl: false
  })
  selectedSize.value = '1920x1080'
}
</script>

<style scoped>
.create-project-container {
  max-height: 70vh;
  overflow-y: auto;
}

.template-preview {
  display: flex;
  gap: 15px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  margin-bottom: 20px;
}

.preview-image {
  width: 120px;
  height: 80px;
  border-radius: 6px;
  overflow: hidden;
  flex-shrink: 0;
}

.preview-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.template-info {
  flex: 1;
}

.template-info h3 {
  margin: 0 0 8px 0;
  font-size: 1.1rem;
  color: #ffffff;
}

.template-info p {
  margin: 0 0 10px 0;
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.9rem;
  line-height: 1.4;
}

.template-meta {
  display: flex;
  gap: 15px;
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.5);
}

.canvas-size-options {
  width: 100%;
}

.custom-size {
  margin-top: 15px;
}

.size-inputs {
  display: flex;
  align-items: center;
  gap: 10px;
}

.size-separator {
  color: rgba(255, 255, 255, 0.6);
  font-weight: 500;
}

.size-unit {
  color: rgba(255, 255, 255, 0.5);
  font-size: 0.9rem;
}

.permission-option {
  margin-left: 8px;
}

.option-title {
  font-weight: 500;
  color: #ffffff;
}

.option-desc {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.6);
  margin-top: 2px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

:deep(.el-form-item__label) {
  color: #ffffff;
}

:deep(.el-input__inner) {
  background: rgba(255, 255, 255, 0.1);
  border-color: #2a3f5f;
  color: #ffffff;
}

:deep(.el-textarea__inner) {
  background: rgba(255, 255, 255, 0.1);
  border-color: #2a3f5f;
  color: #ffffff;
}

:deep(.el-select .el-input__inner) {
  background: rgba(255, 255, 255, 0.1);
  border-color: #2a3f5f;
  color: #ffffff;
}

:deep(.el-radio__label) {
  color: #ffffff;
}

:deep(.el-checkbox__label) {
  color: #ffffff;
}

:deep(.el-radio-button__inner) {
  background: rgba(255, 255, 255, 0.1);
  border-color: #2a3f5f;
  color: #ffffff;
}

:deep(.el-radio-button__original:checked + .el-radio-button__inner) {
  background: #00f5ff;
  border-color: #00f5ff;
  color: #000000;
}
</style>
