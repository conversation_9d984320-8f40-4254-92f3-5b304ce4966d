<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DataV 系统调试</title>
    <style>
        body {
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #0a1a3a 0%, #1a3a8f 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }
        .header p {
            font-size: 1.2rem;
            opacity: 0.8;
        }
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        .feature-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }
        .feature-card:hover {
            background: rgba(255, 255, 255, 0.15);
            transform: translateY(-5px);
        }
        .feature-card h3 {
            margin-top: 0;
            color: #00f5ff;
        }
        .feature-list {
            list-style: none;
            padding: 0;
        }
        .feature-list li {
            padding: 5px 0;
            padding-left: 20px;
            position: relative;
        }
        .feature-list li:before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #00ff88;
            font-weight: bold;
        }
        .nav-buttons {
            display: flex;
            justify-content: center;
            gap: 20px;
            flex-wrap: wrap;
        }
        .nav-button {
            background: rgba(0, 245, 255, 0.2);
            border: 2px solid #00f5ff;
            color: #00f5ff;
            padding: 12px 24px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        .nav-button:hover {
            background: #00f5ff;
            color: #000;
        }
        .status {
            margin-top: 40px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            border-left: 4px solid #00ff88;
        }
        .error {
            border-left-color: #ff4757;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 DataV 可视化大屏构建系统</h1>
            <p>企业级数据可视化解决方案</p>
        </div>

        <div class="features">
            <div class="feature-card">
                <h3>📊 数据源管理</h3>
                <ul class="feature-list">
                    <li>多种数据源类型支持</li>
                    <li>实时数据更新</li>
                    <li>可视化数据预览</li>
                    <li>连接测试和验证</li>
                </ul>
            </div>

            <div class="feature-card">
                <h3>🎨 模板中心</h3>
                <ul class="feature-list">
                    <li>丰富的预设模板</li>
                    <li>分类管理和搜索</li>
                    <li>一键创建项目</li>
                    <li>保存为模板</li>
                </ul>
            </div>

            <div class="feature-card">
                <h3>🛠️ 可视化编辑器</h3>
                <ul class="feature-list">
                    <li>拖拽式组件编辑</li>
                    <li>实时预览效果</li>
                    <li>丰富的图表组件</li>
                    <li>响应式布局</li>
                </ul>
            </div>

            <div class="feature-card">
                <h3>📈 项目管理</h3>
                <ul class="feature-list">
                    <li>项目列表管理</li>
                    <li>版本控制</li>
                    <li>发布部署</li>
                    <li>权限管理</li>
                </ul>
            </div>
        </div>

        <div class="nav-buttons">
            <a href="#" class="nav-button" onclick="testRoute('/')">🏠 首页</a>
            <a href="#" class="nav-button" onclick="testRoute('/projects')">📁 项目管理</a>
            <a href="#" class="nav-button" onclick="testRoute('/datasource')">🔗 数据源管理</a>
            <a href="#" class="nav-button" onclick="testRoute('/templates')">📋 模板中心</a>
            <a href="#" class="nav-button" onclick="testRoute('/editor')">✏️ 编辑器</a>
        </div>

        <div class="status" id="status">
            <h3>系统状态</h3>
            <p>正在检测系统状态...</p>
        </div>
    </div>

    <script>
        function testRoute(path) {
            const status = document.getElementById('status');
            status.innerHTML = `
                <h3>路由测试</h3>
                <p>尝试访问路由: ${path}</p>
                <p>由于开发服务器未启动，无法直接测试路由。</p>
                <p><strong>建议操作：</strong></p>
                <ul>
                    <li>1. 检查 Node.js 和 npm 是否正确安装</li>
                    <li>2. 在项目目录运行: npm install</li>
                    <li>3. 启动开发服务器: npm run dev</li>
                    <li>4. 访问 http://localhost:5173${path}</li>
                </ul>
            `;
        }

        // 检测系统状态
        function checkSystemStatus() {
            const status = document.getElementById('status');
            
            // 检查是否可以访问开发服务器
            fetch('http://localhost:5173/')
                .then(response => {
                    if (response.ok) {
                        status.innerHTML = `
                            <h3>✅ 系统状态正常</h3>
                            <p>开发服务器正在运行</p>
                            <p><a href="http://localhost:5173/" target="_blank" style="color: #00f5ff;">点击访问系统</a></p>
                        `;
                    } else {
                        throw new Error('服务器响应异常');
                    }
                })
                .catch(error => {
                    status.className = 'status error';
                    status.innerHTML = `
                        <h3>❌ 系统状态异常</h3>
                        <p>开发服务器未启动或无法访问</p>
                        <p><strong>解决步骤：</strong></p>
                        <ol>
                            <li>打开命令行，进入项目目录</li>
                            <li>运行: <code>npm install</code></li>
                            <li>运行: <code>npm run dev</code></li>
                            <li>等待服务器启动完成</li>
                            <li>刷新此页面重新检测</li>
                        </ol>
                        <button onclick="checkSystemStatus()" style="margin-top: 10px; padding: 8px 16px; background: #00f5ff; color: #000; border: none; border-radius: 4px; cursor: pointer;">重新检测</button>
                    `;
                });
        }

        // 页面加载时检测状态
        setTimeout(checkSystemStatus, 1000);
    </script>
</body>
</html>
