<template>
  <div class="text-component" :style="textStyle">
    <div v-if="config.type === 'text-title'" class="text-title">
      {{ displayText }}
    </div>
    <div v-else class="text-content">
      {{ displayText }}
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  config: { type: Object, required: true },
  data: { type: Object, default: () => ({}) }
})

const displayText = computed(() => {
  return props.config.text || props.config.name || '文本内容'
})

const textStyle = computed(() => {
  const config = props.config
  return {
    width: '100%',
    height: '100%',
    display: 'flex',
    alignItems: config.verticalAlign || 'center',
    justifyContent: config.textAlign || 'center',
    fontSize: (config.fontSize || 16) + 'px',
    color: config.color || '#ffffff',
    fontWeight: config.fontWeight || 'normal',
    fontFamily: config.fontFamily || 'Microsoft YaHei',
    lineHeight: config.lineHeight || '1.5',
    textShadow: config.textShadow || 'none',
    background: config.backgroundColor || 'transparent',
    border: config.border || 'none',
    borderRadius: config.borderRadius || '0px',
    padding: config.padding || '0px',
    overflow: 'hidden',
    wordBreak: 'break-word'
  }
})
</script>

<style scoped>
.text-component {
  user-select: none;
}

.text-title {
  font-weight: bold;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.text-content {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
