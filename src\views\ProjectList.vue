<template>
  <div class="project-list-container">
    <!-- 顶部导航 -->
    <header class="header">
      <div class="header-content">
        <div class="logo" @click="goHome">
          <img src="/favicon.ico" alt="DataV" class="logo-icon">
          <span class="logo-text">DataV</span>
        </div>
        <div class="header-actions">
          <el-button type="primary" @click="createProject">
            <el-icon><Plus /></el-icon>
            新建项目
          </el-button>
        </div>
      </div>
    </header>

    <!-- 主要内容 -->
    <main class="main-content">
      <!-- 工具栏 -->
      <div class="toolbar">
        <div class="toolbar-left">
          <h1 class="page-title">我的项目</h1>
          <span class="project-count">共 {{ projectList.length }} 个项目</span>
        </div>
        <div class="toolbar-right">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索项目..."
            style="width: 300px"
            @input="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
          <el-select v-model="statusFilter" placeholder="状态筛选" style="width: 120px" @change="handleFilter">
            <el-option label="全部" value="" />
            <el-option label="草稿" value="draft" />
            <el-option label="已发布" value="published" />
          </el-select>
        </div>
      </div>

      <!-- 项目列表 -->
      <div class="project-grid" v-loading="loading">
        <div
          v-for="project in filteredProjects"
          :key="project.id"
          class="project-card"
          @click="editProject(project.id)"
        >
          <div class="project-thumbnail">
            <img :src="project.thumbnail || '/images/default-project.jpg'" :alt="project.name">
            <div class="project-overlay">
              <div class="project-actions">
                <el-button type="primary" size="small" @click.stop="editProject(project.id)">
                  <el-icon><Edit /></el-icon>
                  编辑
                </el-button>
                <el-button size="small" @click.stop="previewProject(project.id)">
                  <el-icon><View /></el-icon>
                  预览
                </el-button>
                <el-dropdown @command="handleCommand" trigger="click" @click.stop>
                  <el-button size="small">
                    <el-icon><More /></el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item :command="{action: 'copy', id: project.id}">
                        <el-icon><CopyDocument /></el-icon>
                        复制
                      </el-dropdown-item>
                      <el-dropdown-item :command="{action: 'publish', id: project.id}" v-if="project.status === 'draft'">
                        <el-icon><Upload /></el-icon>
                        发布
                      </el-dropdown-item>
                      <el-dropdown-item :command="{action: 'delete', id: project.id}" divided>
                        <el-icon><Delete /></el-icon>
                        删除
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </div>
          </div>
          <div class="project-info">
            <h3 class="project-name">{{ project.name }}</h3>
            <p class="project-desc">{{ project.description }}</p>
            <div class="project-meta">
              <span class="project-status" :class="project.status">
                {{ project.status === 'draft' ? '草稿' : '已发布' }}
              </span>
              <span class="project-time">{{ project.updateTime }}</span>
            </div>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-if="filteredProjects.length === 0 && !loading" class="empty-state">
          <el-icon size="80"><FolderOpened /></el-icon>
          <h3>暂无项目</h3>
          <p>点击"新建项目"开始创建您的第一个可视化大屏</p>
          <el-button type="primary" @click="createProject">
            <el-icon><Plus /></el-icon>
            新建项目
          </el-button>
        </div>
      </div>
    </main>

    <!-- 新建项目对话框 -->
    <el-dialog v-model="createDialogVisible" title="新建项目" width="500px">
      <el-form :model="newProject" label-width="80px">
        <el-form-item label="项目名称" required>
          <el-input v-model="newProject.name" placeholder="请输入项目名称" />
        </el-form-item>
        <el-form-item label="项目描述">
          <el-input
            v-model="newProject.description"
            type="textarea"
            :rows="3"
            placeholder="请输入项目描述"
          />
        </el-form-item>
        <el-form-item label="画布尺寸">
          <div class="canvas-size">
            <el-input v-model.number="newProject.width" placeholder="宽度" style="width: 100px" />
            <span style="margin: 0 10px">×</span>
            <el-input v-model.number="newProject.height" placeholder="高度" style="width: 100px" />
            <span style="margin-left: 10px; color: #999">px</span>
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="createDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleCreateProject">创建</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getProjectList, createProject as createProjectApi, deleteProject, copyProject, publishProject } from '@/api/projectApi'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const projectList = ref([])
const searchKeyword = ref('')
const statusFilter = ref('')
const createDialogVisible = ref(false)

const newProject = reactive({
  name: '',
  description: '',
  width: 1920,
  height: 1080,
  backgroundColor: '#0a1a3a'
})

// 计算属性
const filteredProjects = computed(() => {
  let projects = [...projectList.value]
  
  // 搜索过滤
  if (searchKeyword.value) {
    projects = projects.filter(project => 
      project.name.includes(searchKeyword.value) || 
      project.description.includes(searchKeyword.value)
    )
  }
  
  // 状态过滤
  if (statusFilter.value) {
    projects = projects.filter(project => project.status === statusFilter.value)
  }
  
  return projects
})

// 方法
const loadProjects = async () => {
  loading.value = true
  try {
    const res = await getProjectList()
    if (res.code === 200) {
      projectList.value = res.data.list
    }
  } catch (error) {
    ElMessage.error('加载项目列表失败')
  } finally {
    loading.value = false
  }
}

const goHome = () => {
  router.push('/')
}

const createProject = () => {
  createDialogVisible.value = true
}

const handleCreateProject = async () => {
  if (!newProject.name.trim()) {
    ElMessage.warning('请输入项目名称')
    return
  }

  try {
    const res = await createProjectApi(newProject)
    if (res.code === 200) {
      ElMessage.success('项目创建成功')
      createDialogVisible.value = false
      router.push(`/editor/${res.data.id}`)
    }
  } catch (error) {
    ElMessage.error('创建项目失败')
  }
}

const editProject = (id) => {
  router.push(`/editor/${id}`)
}

const previewProject = (id) => {
  router.push(`/preview/${id}`)
}

const handleCommand = async (command) => {
  const { action, id } = command
  
  switch (action) {
    case 'copy':
      try {
        const res = await copyProject(id)
        if (res.code === 200) {
          ElMessage.success('项目复制成功')
          loadProjects()
        }
      } catch (error) {
        ElMessage.error('复制项目失败')
      }
      break
      
    case 'publish':
      try {
        const res = await publishProject(id)
        if (res.code === 200) {
          ElMessage.success('项目发布成功')
          loadProjects()
        }
      } catch (error) {
        ElMessage.error('发布项目失败')
      }
      break
      
    case 'delete':
      ElMessageBox.confirm('确定要删除这个项目吗？', '确认删除', {
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          const res = await deleteProject(id)
          if (res.code === 200) {
            ElMessage.success('项目删除成功')
            loadProjects()
          }
        } catch (error) {
          ElMessage.error('删除项目失败')
        }
      })
      break
  }
}

const handleSearch = () => {
  // 搜索逻辑已在计算属性中处理
}

const handleFilter = () => {
  // 过滤逻辑已在计算属性中处理
}

// 生命周期
onMounted(() => {
  loadProjects()
})
</script>

<style scoped>
.project-list-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #0a1a3a 0%, #1a3a8f 100%);
  color: #ffffff;
}

.header {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding: 0 2rem;
}

.header-content {
  max-width: 1400px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 70px;
}

.logo {
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
}

.logo-icon {
  width: 32px;
  height: 32px;
}

.logo-text {
  font-size: 20px;
  font-weight: 600;
  color: #ffffff;
}

.main-content {
  max-width: 1400px;
  margin: 0 auto;
  padding: 2rem;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.page-title {
  font-size: 2rem;
  font-weight: 600;
  margin: 0;
}

.project-count {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.9rem;
}

.toolbar-right {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.project-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 2rem;
}

.project-card {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
}

.project-card:hover {
  transform: translateY(-5px);
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(0, 245, 255, 0.3);
}

.project-thumbnail {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.project-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.project-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.project-card:hover .project-overlay {
  opacity: 1;
}

.project-actions {
  display: flex;
  gap: 0.5rem;
}

.project-info {
  padding: 1.5rem;
}

.project-name {
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
  color: #ffffff;
}

.project-desc {
  color: rgba(255, 255, 255, 0.7);
  margin: 0 0 1rem 0;
  font-size: 0.9rem;
  line-height: 1.4;
}

.project-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.8rem;
}

.project-status {
  padding: 0.2rem 0.5rem;
  border-radius: 4px;
  font-size: 0.7rem;
  font-weight: 500;
}

.project-status.draft {
  background: rgba(255, 170, 51, 0.2);
  color: #ffaa33;
}

.project-status.published {
  background: rgba(0, 255, 163, 0.2);
  color: #00ffa3;
}

.project-time {
  color: rgba(255, 255, 255, 0.5);
}

.empty-state {
  grid-column: 1 / -1;
  text-align: center;
  padding: 4rem 2rem;
  color: rgba(255, 255, 255, 0.7);
}

.empty-state h3 {
  margin: 1rem 0;
  color: #ffffff;
}

.canvas-size {
  display: flex;
  align-items: center;
}

@media (max-width: 768px) {
  .toolbar {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }
  
  .toolbar-right {
    flex-direction: column;
  }
  
  .project-grid {
    grid-template-columns: 1fr;
  }
}
</style>
