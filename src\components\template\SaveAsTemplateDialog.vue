<template>
  <el-dialog
    v-model="visible"
    title="保存为模板"
    width="600px"
    :before-close="handleClose"
  >
    <div class="save-template-container">
      <!-- 项目预览 -->
      <div class="project-preview">
        <div class="preview-image">
          <div class="canvas-preview" :style="canvasPreviewStyle">
            <div class="preview-placeholder">
              <el-icon size="40"><Document /></el-icon>
              <p>项目预览</p>
            </div>
          </div>
        </div>
        <div class="project-info">
          <h3>{{ projectName }}</h3>
          <p>{{ projectDescription || '暂无描述' }}</p>
          <div class="project-meta">
            <span class="size">{{ projectWidth }}×{{ projectHeight }}</span>
            <span class="components">{{ componentCount }} 个组件</span>
          </div>
        </div>
      </div>

      <!-- 模板配置表单 -->
      <el-form :model="formData" :rules="rules" ref="formRef" label-width="100px">
        <el-form-item label="模板名称" prop="name">
          <el-input
            v-model="formData.name"
            placeholder="请输入模板名称"
            maxlength="50"
            show-word-limit
          />
        </el-form-item>
        
        <el-form-item label="模板描述" prop="description">
          <el-input
            v-model="formData.description"
            type="textarea"
            :rows="3"
            placeholder="请输入模板描述，让其他人了解这个模板的用途"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>
        
        <el-form-item label="模板分类" prop="category">
          <el-select
            v-model="formData.category"
            placeholder="选择模板分类"
            style="width: 100%"
          >
            <el-option
              v-for="category in categories"
              :key="category.id"
              :label="category.name"
              :value="category.id"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="模板标签">
          <div class="tags-input">
            <el-tag
              v-for="tag in formData.tags"
              :key="tag"
              closable
              @close="removeTag(tag)"
              style="margin-right: 8px; margin-bottom: 8px;"
            >
              {{ tag }}
            </el-tag>
            <el-input
              v-if="inputVisible"
              ref="inputRef"
              v-model="inputValue"
              size="small"
              style="width: 100px;"
              @keyup.enter="handleInputConfirm"
              @blur="handleInputConfirm"
            />
            <el-button
              v-else
              size="small"
              @click="showInput"
            >
              + 添加标签
            </el-button>
          </div>
        </el-form-item>
        
        <el-form-item label="访问权限">
          <el-radio-group v-model="formData.visibility">
            <el-radio label="private">
              <div class="permission-option">
                <div class="option-title">私有</div>
                <div class="option-desc">只有您可以使用</div>
              </div>
            </el-radio>
            <el-radio label="team">
              <div class="permission-option">
                <div class="option-title">团队</div>
                <div class="option-desc">团队成员可以使用</div>
              </div>
            </el-radio>
            <el-radio label="public">
              <div class="permission-option">
                <div class="option-title">公开</div>
                <div class="option-desc">所有人都可以使用</div>
              </div>
            </el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="高级选项">
          <el-checkbox v-model="formData.includeData">包含示例数据</el-checkbox>
          <el-checkbox v-model="formData.includeDataSource">包含数据源配置</el-checkbox>
        </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSave" :loading="saving">
          保存为模板
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, nextTick, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { saveAsTemplate } from '@/api/templateApi'
import { getTemplateCategories } from '@/api/templateApi'

const props = defineProps({
  modelValue: { type: Boolean, default: false },
  project: { type: Object, default: null }
})

const emit = defineEmits(['update:modelValue', 'saved'])

// 响应式数据
const formRef = ref(null)
const inputRef = ref(null)
const saving = ref(false)
const inputVisible = ref(false)
const inputValue = ref('')
const categories = ref([])

const formData = reactive({
  name: '',
  description: '',
  category: '',
  tags: [],
  visibility: 'private',
  includeData: true,
  includeDataSource: false
})

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const projectName = computed(() => props.project?.name || '未命名项目')
const projectDescription = computed(() => props.project?.description || '')
const projectWidth = computed(() => props.project?.width || 1920)
const projectHeight = computed(() => props.project?.height || 1080)
const componentCount = computed(() => props.project?.components?.length || 0)

const canvasPreviewStyle = computed(() => ({
  width: '100%',
  height: '100%',
  background: 'linear-gradient(135deg, #0a1a3a 0%, #1a3a8f 100%)',
  borderRadius: '4px',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  color: 'rgba(255, 255, 255, 0.5)'
}))

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入模板名称', trigger: 'blur' },
    { min: 2, max: 50, message: '模板名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入模板描述', trigger: 'blur' },
    { min: 10, max: 200, message: '模板描述长度在 10 到 200 个字符', trigger: 'blur' }
  ],
  category: [
    { required: true, message: '请选择模板分类', trigger: 'change' }
  ]
}

// 监听项目变化
watch(() => props.project, (newProject) => {
  if (newProject) {
    formData.name = newProject.name + ' 模板'
    formData.description = newProject.description || ''
    
    // 根据项目内容自动生成标签
    const autoTags = []
    if (newProject.components) {
      const componentTypes = [...new Set(newProject.components.map(c => c.type))]
      if (componentTypes.includes('bar-chart') || componentTypes.includes('line-chart')) {
        autoTags.push('图表')
      }
      if (componentTypes.includes('table')) {
        autoTags.push('表格')
      }
      if (componentTypes.includes('gauge') || componentTypes.includes('counter')) {
        autoTags.push('指标')
      }
    }
    formData.tags = autoTags
  }
}, { immediate: true })

// 监听对话框显示
watch(visible, (show) => {
  if (show) {
    loadCategories()
  }
})

// 方法
const loadCategories = async () => {
  try {
    const res = await getTemplateCategories()
    if (res.code === 200) {
      categories.value = res.data
    }
  } catch (error) {
    console.error('加载分类失败:', error)
  }
}

const removeTag = (tag) => {
  const index = formData.tags.indexOf(tag)
  if (index !== -1) {
    formData.tags.splice(index, 1)
  }
}

const showInput = () => {
  inputVisible.value = true
  nextTick(() => {
    inputRef.value?.focus()
  })
}

const handleInputConfirm = () => {
  if (inputValue.value && !formData.tags.includes(inputValue.value)) {
    formData.tags.push(inputValue.value)
  }
  inputVisible.value = false
  inputValue.value = ''
}

const handleSave = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    saving.value = true
    
    const templateData = {
      name: formData.name,
      description: formData.description,
      category: formData.category,
      categoryName: categories.value.find(c => c.id === formData.category)?.name || '',
      tags: formData.tags,
      visibility: formData.visibility,
      width: projectWidth.value,
      height: projectHeight.value,
      thumbnail: '/images/templates/custom-template.jpg', // 这里应该生成实际的缩略图
      components: formData.includeData ? props.project.components : 
                  props.project.components?.map(c => ({ ...c, data: undefined })),
      dataSources: formData.includeDataSource ? props.project.dataSources : undefined,
      isNew: true,
      isHot: false,
      isPro: false
    }
    
    const res = await saveAsTemplate(templateData)
    
    if (res.code === 200) {
      ElMessage.success('模板保存成功')
      emit('saved', res.data)
      handleClose()
    } else {
      ElMessage.error(res.msg || '保存模板失败')
    }
  } catch (error) {
    if (error !== false) { // 表单验证失败时会返回false
      ElMessage.error('保存模板失败')
    }
  } finally {
    saving.value = false
  }
}

const handleClose = () => {
  visible.value = false
  resetForm()
}

const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  Object.assign(formData, {
    name: '',
    description: '',
    category: '',
    tags: [],
    visibility: 'private',
    includeData: true,
    includeDataSource: false
  })
  inputVisible.value = false
  inputValue.value = ''
}
</script>

<style scoped>
.save-template-container {
  max-height: 70vh;
  overflow-y: auto;
}

.project-preview {
  display: flex;
  gap: 15px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  margin-bottom: 20px;
}

.preview-image {
  width: 120px;
  height: 80px;
  border-radius: 6px;
  overflow: hidden;
  flex-shrink: 0;
}

.canvas-preview {
  font-size: 12px;
  text-align: center;
}

.preview-placeholder p {
  margin: 5px 0 0 0;
}

.project-info {
  flex: 1;
}

.project-info h3 {
  margin: 0 0 8px 0;
  font-size: 1.1rem;
  color: #ffffff;
}

.project-info p {
  margin: 0 0 10px 0;
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.9rem;
  line-height: 1.4;
}

.project-meta {
  display: flex;
  gap: 15px;
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.5);
}

.tags-input {
  width: 100%;
  min-height: 32px;
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
}

.permission-option {
  margin-left: 8px;
}

.option-title {
  font-weight: 500;
  color: #ffffff;
}

.option-desc {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.6);
  margin-top: 2px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

:deep(.el-form-item__label) {
  color: #ffffff;
}

:deep(.el-input__inner) {
  background: rgba(255, 255, 255, 0.1);
  border-color: #2a3f5f;
  color: #ffffff;
}

:deep(.el-textarea__inner) {
  background: rgba(255, 255, 255, 0.1);
  border-color: #2a3f5f;
  color: #ffffff;
}

:deep(.el-select .el-input__inner) {
  background: rgba(255, 255, 255, 0.1);
  border-color: #2a3f5f;
  color: #ffffff;
}

:deep(.el-radio__label) {
  color: #ffffff;
}

:deep(.el-checkbox__label) {
  color: #ffffff;
}

:deep(.el-tag) {
  background: rgba(0, 245, 255, 0.2);
  border-color: rgba(0, 245, 255, 0.3);
  color: #00f5ff;
}
</style>
