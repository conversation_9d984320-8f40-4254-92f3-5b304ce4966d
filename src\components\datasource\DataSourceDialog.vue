<template>
  <el-dialog
    v-model="visible"
    :title="isEdit ? '编辑数据源' : '新建数据源'"
    width="800px"
    :before-close="handleClose"
  >
    <el-form :model="formData" :rules="rules" ref="formRef" label-width="100px">
      <!-- 基本信息 -->
      <div class="form-section">
        <h4>基本信息</h4>
        <el-form-item label="数据源名称" prop="name">
          <el-input v-model="formData.name" placeholder="请输入数据源名称" />
        </el-form-item>
        <el-form-item label="数据源类型" prop="type">
          <el-select v-model="formData.type" placeholder="选择数据源类型" style="width: 100%" @change="handleTypeChange">
            <el-option label="API接口" value="api" />
            <el-option label="MySQL数据库" value="mysql" />
            <el-option label="PostgreSQL数据库" value="postgresql" />
            <el-option label="MongoDB数据库" value="mongodb" />
            <el-option label="静态数据" value="static" />
            <el-option label="CSV文件" value="csv" />
            <el-option label="JSON文件" value="json" />
          </el-select>
        </el-form-item>
        <el-form-item label="描述">
          <el-input v-model="formData.description" type="textarea" :rows="2" placeholder="请输入数据源描述" />
        </el-form-item>
      </div>

      <!-- API配置 -->
      <div v-if="formData.type === 'api'" class="form-section">
        <h4>API配置</h4>
        <el-form-item label="请求地址" prop="url">
          <el-input v-model="formData.url" placeholder="https://api.example.com/data" />
        </el-form-item>
        <el-form-item label="请求方法" prop="method">
          <el-select v-model="formData.method" style="width: 100%">
            <el-option label="GET" value="GET" />
            <el-option label="POST" value="POST" />
            <el-option label="PUT" value="PUT" />
            <el-option label="DELETE" value="DELETE" />
          </el-select>
        </el-form-item>
        <el-form-item label="请求头">
          <div class="headers-editor">
            <div v-for="(header, index) in formData.headers" :key="index" class="header-row">
              <el-input v-model="header.key" placeholder="Header名称" style="width: 40%" />
              <el-input v-model="header.value" placeholder="Header值" style="width: 40%" />
              <el-button @click="removeHeader(index)" type="danger" size="small">删除</el-button>
            </div>
            <el-button @click="addHeader" type="primary" size="small">添加Header</el-button>
          </div>
        </el-form-item>
        <el-form-item label="请求参数" v-if="formData.method !== 'GET'">
          <el-input v-model="formData.body" type="textarea" :rows="4" placeholder="JSON格式的请求体" />
        </el-form-item>
        <el-form-item label="查询参数" v-if="formData.method === 'GET'">
          <div class="params-editor">
            <div v-for="(param, index) in formData.params" :key="index" class="param-row">
              <el-input v-model="param.key" placeholder="参数名" style="width: 40%" />
              <el-input v-model="param.value" placeholder="参数值" style="width: 40%" />
              <el-button @click="removeParam(index)" type="danger" size="small">删除</el-button>
            </div>
            <el-button @click="addParam" type="primary" size="small">添加参数</el-button>
          </div>
        </el-form-item>
      </div>

      <!-- 数据库配置 -->
      <div v-if="isDatabaseType" class="form-section">
        <h4>数据库配置</h4>
        <el-form-item label="主机地址" prop="host">
          <el-input v-model="formData.host" placeholder="localhost" />
        </el-form-item>
        <el-form-item label="端口" prop="port">
          <el-input-number v-model="formData.port" :min="1" :max="65535" style="width: 100%" />
        </el-form-item>
        <el-form-item label="数据库名" prop="database">
          <el-input v-model="formData.database" placeholder="数据库名称" />
        </el-form-item>
        <el-form-item label="用户名" prop="username">
          <el-input v-model="formData.username" placeholder="数据库用户名" />
        </el-form-item>
        <el-form-item label="密码" prop="password">
          <el-input v-model="formData.password" type="password" placeholder="数据库密码" show-password />
        </el-form-item>
        <el-form-item label="查询语句" prop="query">
          <el-input v-model="formData.query" type="textarea" :rows="4" placeholder="SELECT * FROM table_name" />
        </el-form-item>
      </div>

      <!-- 静态数据配置 -->
      <div v-if="formData.type === 'static'" class="form-section">
        <h4>静态数据</h4>
        <el-form-item label="数据内容" prop="staticData">
          <el-input v-model="formData.staticData" type="textarea" :rows="8" placeholder="JSON格式的数据" />
        </el-form-item>
        <div class="data-tools">
          <el-button @click="formatJSON" size="small">格式化JSON</el-button>
          <el-button @click="validateJSON" size="small">验证JSON</el-button>
          <el-button @click="generateSampleData" size="small">生成示例数据</el-button>
        </div>
      </div>

      <!-- 文件配置 -->
      <div v-if="isFileType" class="form-section">
        <h4>文件配置</h4>
        <el-form-item label="文件地址" prop="fileUrl">
          <el-input v-model="formData.fileUrl" placeholder="文件URL或路径" />
        </el-form-item>
        <el-form-item label="文件上传">
          <el-upload
            class="upload-demo"
            drag
            :auto-upload="false"
            :on-change="handleFileChange"
            :show-file-list="false"
          >
            <el-icon class="el-icon--upload"><UploadFilled /></el-icon>
            <div class="el-upload__text">
              将文件拖到此处，或<em>点击上传</em>
            </div>
            <template #tip>
              <div class="el-upload__tip">
                支持 {{ formData.type === 'csv' ? 'CSV' : 'JSON' }} 格式文件
              </div>
            </template>
          </el-upload>
        </el-form-item>
      </div>

      <!-- 高级设置 -->
      <div class="form-section">
        <h4>高级设置</h4>
        <el-form-item label="自动刷新">
          <el-switch v-model="enableAutoRefresh" />
        </el-form-item>
        <el-form-item label="刷新间隔" v-if="enableAutoRefresh">
          <div style="display: flex; align-items: center; gap: 10px;">
            <el-input-number v-model="refreshInterval" :min="1" :max="3600" style="width: 150px" />
            <span>秒</span>
          </div>
        </el-form-item>
        <el-form-item label="超时时间">
          <div style="display: flex; align-items: center; gap: 10px;">
            <el-input-number v-model="formData.timeout" :min="1" :max="300" style="width: 150px" />
            <span>秒</span>
          </div>
        </el-form-item>
      </div>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="testConnection" :loading="testing">
          <el-icon><Connection /></el-icon>
          测试连接
        </el-button>
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSave" :loading="saving">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { createDataSource, updateDataSource, testDataSource } from '@/api/dataSourceApi'

const props = defineProps({
  modelValue: { type: Boolean, default: false },
  datasource: { type: Object, default: null }
})

const emit = defineEmits(['update:modelValue', 'save'])

// 响应式数据
const formRef = ref(null)
const testing = ref(false)
const saving = ref(false)
const enableAutoRefresh = ref(false)
const refreshInterval = ref(30)

const formData = reactive({
  name: '',
  type: 'api',
  description: '',
  url: '',
  method: 'GET',
  headers: [{ key: 'Content-Type', value: 'application/json' }],
  params: [],
  body: '',
  host: '',
  port: 3306,
  database: '',
  username: '',
  password: '',
  query: '',
  staticData: '',
  fileUrl: '',
  timeout: 30
})

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const isEdit = computed(() => !!props.datasource)
const isDatabaseType = computed(() => ['mysql', 'postgresql', 'mongodb'].includes(formData.type))
const isFileType = computed(() => ['csv', 'json'].includes(formData.type))

// 表单验证规则
const rules = {
  name: [{ required: true, message: '请输入数据源名称', trigger: 'blur' }],
  type: [{ required: true, message: '请选择数据源类型', trigger: 'change' }],
  url: [{ required: true, message: '请输入API地址', trigger: 'blur' }],
  host: [{ required: true, message: '请输入主机地址', trigger: 'blur' }],
  database: [{ required: true, message: '请输入数据库名', trigger: 'blur' }],
  username: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
  query: [{ required: true, message: '请输入查询语句', trigger: 'blur' }],
  staticData: [{ required: true, message: '请输入静态数据', trigger: 'blur' }],
  fileUrl: [{ required: true, message: '请输入文件地址', trigger: 'blur' }]
}

// 监听数据源变化
watch(() => props.datasource, (newDataSource) => {
  if (newDataSource) {
    Object.assign(formData, {
      ...newDataSource,
      headers: newDataSource.headers ? Object.entries(newDataSource.headers).map(([key, value]) => ({ key, value })) : [{ key: 'Content-Type', value: 'application/json' }],
      params: newDataSource.params ? Object.entries(newDataSource.params).map(([key, value]) => ({ key, value })) : []
    })
    enableAutoRefresh.value = !!newDataSource.updateInterval
    refreshInterval.value = newDataSource.updateInterval ? newDataSource.updateInterval / 1000 : 30
  } else {
    resetForm()
  }
}, { immediate: true })

// 方法
const resetForm = () => {
  Object.assign(formData, {
    name: '',
    type: 'api',
    description: '',
    url: '',
    method: 'GET',
    headers: [{ key: 'Content-Type', value: 'application/json' }],
    params: [],
    body: '',
    host: '',
    port: 3306,
    database: '',
    username: '',
    password: '',
    query: '',
    staticData: '',
    fileUrl: '',
    timeout: 30
  })
  enableAutoRefresh.value = false
  refreshInterval.value = 30
}

const handleTypeChange = () => {
  // 根据类型设置默认端口
  if (formData.type === 'mysql') {
    formData.port = 3306
  } else if (formData.type === 'postgresql') {
    formData.port = 5432
  } else if (formData.type === 'mongodb') {
    formData.port = 27017
  }
}

const addHeader = () => {
  formData.headers.push({ key: '', value: '' })
}

const removeHeader = (index) => {
  formData.headers.splice(index, 1)
}

const addParam = () => {
  formData.params.push({ key: '', value: '' })
}

const removeParam = (index) => {
  formData.params.splice(index, 1)
}

const formatJSON = () => {
  try {
    const parsed = JSON.parse(formData.staticData)
    formData.staticData = JSON.stringify(parsed, null, 2)
    ElMessage.success('JSON格式化成功')
  } catch (error) {
    ElMessage.error('JSON格式错误')
  }
}

const validateJSON = () => {
  try {
    JSON.parse(formData.staticData)
    ElMessage.success('JSON格式正确')
  } catch (error) {
    ElMessage.error('JSON格式错误: ' + error.message)
  }
}

const generateSampleData = () => {
  const sampleData = [
    { id: 1, name: '示例数据1', value: 100, category: 'A' },
    { id: 2, name: '示例数据2', value: 200, category: 'B' },
    { id: 3, name: '示例数据3', value: 150, category: 'A' },
    { id: 4, name: '示例数据4', value: 300, category: 'C' }
  ]
  formData.staticData = JSON.stringify(sampleData, null, 2)
}

const handleFileChange = (file) => {
  const reader = new FileReader()
  reader.onload = (e) => {
    if (formData.type === 'json') {
      formData.staticData = e.target.result
    } else if (formData.type === 'csv') {
      // 简单的CSV解析
      const lines = e.target.result.split('\n')
      const headers = lines[0].split(',')
      const data = lines.slice(1).map(line => {
        const values = line.split(',')
        const obj = {}
        headers.forEach((header, index) => {
          obj[header.trim()] = values[index]?.trim()
        })
        return obj
      }).filter(obj => Object.values(obj).some(v => v))
      formData.staticData = JSON.stringify(data, null, 2)
    }
  }
  reader.readAsText(file.raw)
}

const testConnection = async () => {
  testing.value = true
  try {
    const testData = {
      ...formData,
      headers: formData.headers.reduce((acc, header) => {
        if (header.key && header.value) {
          acc[header.key] = header.value
        }
        return acc
      }, {}),
      params: formData.params.reduce((acc, param) => {
        if (param.key && param.value) {
          acc[param.key] = param.value
        }
        return acc
      }, {})
    }
    
    const res = await testDataSource(testData)
    if (res.code === 200) {
      ElMessage.success('连接测试成功')
    } else {
      ElMessage.error('连接测试失败: ' + res.msg)
    }
  } catch (error) {
    ElMessage.error('连接测试失败')
  } finally {
    testing.value = false
  }
}

const handleSave = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    saving.value = true
    
    const saveData = {
      ...formData,
      headers: formData.headers.reduce((acc, header) => {
        if (header.key && header.value) {
          acc[header.key] = header.value
        }
        return acc
      }, {}),
      params: formData.params.reduce((acc, param) => {
        if (param.key && param.value) {
          acc[param.key] = param.value
        }
        return acc
      }, {}),
      updateInterval: enableAutoRefresh.value ? refreshInterval.value * 1000 : 0
    }
    
    let res
    if (isEdit.value) {
      res = await updateDataSource(props.datasource.id, saveData)
    } else {
      res = await createDataSource(saveData)
    }
    
    if (res.code === 200) {
      ElMessage.success(isEdit.value ? '数据源更新成功' : '数据源创建成功')
      emit('save')
      handleClose()
    } else {
      ElMessage.error(res.msg || '操作失败')
    }
  } catch (error) {
    console.error('保存失败:', error)
  } finally {
    saving.value = false
  }
}

const handleClose = () => {
  visible.value = false
  resetForm()
}
</script>

<style scoped>
.form-section {
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #eee;
}

.form-section:last-child {
  border-bottom: none;
}

.form-section h4 {
  margin: 0 0 1rem 0;
  color: #00f5ff;
  font-size: 1rem;
}

.headers-editor, .params-editor {
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 1rem;
  background: #f9f9f9;
}

.header-row, .param-row {
  display: flex;
  gap: 10px;
  margin-bottom: 10px;
  align-items: center;
}

.data-tools {
  margin-top: 10px;
  display: flex;
  gap: 10px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

:deep(.el-dialog__body) {
  max-height: 70vh;
  overflow-y: auto;
}
</style>
