/**
 * 模板相关API
 */

// 模拟模板分类数据
const mockCategories = [
  {
    id: 'business',
    name: '商业分析',
    icon: 'TrendCharts',
    count: 12
  },
  {
    id: 'monitor',
    name: '监控大屏',
    icon: 'Monitor',
    count: 8
  },
  {
    id: 'finance',
    name: '金融财务',
    icon: 'Coin',
    count: 6
  },
  {
    id: 'ecommerce',
    name: '电商零售',
    icon: 'ShoppingCart',
    count: 10
  },
  {
    id: 'logistics',
    name: '物流运输',
    icon: 'Truck',
    count: 5
  },
  {
    id: 'education',
    name: '教育培训',
    icon: 'Reading',
    count: 4
  },
  {
    id: 'healthcare',
    name: '医疗健康',
    icon: 'FirstAidKit',
    count: 3
  },
  {
    id: 'government',
    name: '政府公共',
    icon: 'OfficeBuilding',
    count: 7
  }
]

// 模拟模板数据
const mockTemplates = [
  {
    id: 1,
    name: '销售业绩分析大屏',
    description: '展示销售团队业绩、区域销售分布、产品销量排行等关键指标，适用于销售管理和业绩监控。',
    category: 'business',
    categoryName: '商业分析',
    thumbnail: '/images/templates/sales-dashboard.jpg',
    width: 1920,
    height: 1080,
    tags: ['销售', '业绩', '分析', '图表'],
    isNew: true,
    isHot: true,
    isPro: false,
    usageCount: 1250,
    createTime: '2024-01-20 10:30:00',
    updateTime: '2024-01-22 14:20:00',
    author: 'DataV官方',
    components: [
      {
        id: 'title-1',
        type: 'text-title',
        name: '销售业绩分析大屏',
        x: 760,
        y: 30,
        width: 400,
        height: 60,
        text: '销售业绩分析大屏',
        fontSize: 32,
        color: '#ffffff',
        textAlign: 'center'
      },
      {
        id: 'chart-1',
        type: 'bar-chart',
        name: '月度销售趋势',
        x: 50,
        y: 120,
        width: 600,
        height: 400,
        data: [
          { name: '1月', value: 120000 },
          { name: '2月', value: 150000 },
          { name: '3月', value: 180000 },
          { name: '4月', value: 160000 },
          { name: '5月', value: 200000 },
          { name: '6月', value: 220000 }
        ],
        config: {
          title: '月度销售趋势',
          colorScheme: 'tech-blue'
        }
      },
      {
        id: 'chart-2',
        type: 'pie-chart',
        name: '产品销量分布',
        x: 700,
        y: 120,
        width: 500,
        height: 400,
        data: [
          { name: '产品A', value: 35 },
          { name: '产品B', value: 25 },
          { name: '产品C', value: 20 },
          { name: '产品D', value: 15 },
          { name: '产品E', value: 5 }
        ],
        config: {
          title: '产品销量分布',
          showLegend: true
        }
      },
      {
        id: 'counter-1',
        type: 'counter',
        name: '总销售额',
        x: 1270,
        y: 120,
        width: 300,
        height: 120,
        value: 1250000,
        config: {
          title: '总销售额',
          prefix: '￥',
          precision: 0,
          showTitle: true
        }
      },
      {
        id: 'gauge-1',
        type: 'gauge',
        name: '目标完成率',
        x: 1270,
        y: 280,
        width: 300,
        height: 240,
        value: 85,
        config: {
          title: '目标完成率',
          unit: '%',
          max: 100
        }
      },
      {
        id: 'table-1',
        type: 'table',
        name: '销售排行榜',
        x: 50,
        y: 580,
        width: 800,
        height: 400,
        data: [
          { rank: 1, name: '张三', sales: 150000, target: 120000, completion: 125 },
          { rank: 2, name: '李四', sales: 135000, target: 130000, completion: 104 },
          { rank: 3, name: '王五', sales: 128000, target: 125000, completion: 102 },
          { rank: 4, name: '赵六', sales: 115000, target: 120000, completion: 96 },
          { rank: 5, name: '钱七', sales: 108000, target: 110000, completion: 98 }
        ],
        columns: [
          { key: 'rank', title: '排名', width: '80px' },
          { key: 'name', title: '姓名', width: '120px' },
          { key: 'sales', title: '销售额', width: '150px', format: 'currency' },
          { key: 'target', title: '目标', width: '150px', format: 'currency' },
          { key: 'completion', title: '完成率(%)', width: '120px' }
        ],
        config: {
          title: '销售排行榜',
          showTitle: true
        }
      }
    ]
  },
  {
    id: 2,
    name: '系统监控大屏',
    description: '实时监控系统运行状态、服务器性能、网络流量等技术指标，适用于运维监控和系统管理。',
    category: 'monitor',
    categoryName: '监控大屏',
    thumbnail: '/images/templates/system-monitor.jpg',
    width: 1920,
    height: 1080,
    tags: ['监控', '系统', '运维', '实时'],
    isNew: false,
    isHot: true,
    isPro: true,
    usageCount: 890,
    createTime: '2024-01-18 15:45:00',
    updateTime: '2024-01-21 09:30:00',
    author: 'DataV官方',
    components: [
      {
        id: 'title-1',
        type: 'text-title',
        name: '系统监控大屏',
        x: 760,
        y: 30,
        width: 400,
        height: 60,
        text: '系统监控大屏',
        fontSize: 32,
        color: '#00ff88'
      },
      {
        id: 'gauge-1',
        type: 'gauge',
        name: 'CPU使用率',
        x: 50,
        y: 120,
        width: 300,
        height: 300,
        value: 65,
        config: {
          title: 'CPU使用率',
          unit: '%',
          max: 100
        }
      },
      {
        id: 'gauge-2',
        type: 'gauge',
        name: '内存使用率',
        x: 400,
        y: 120,
        width: 300,
        height: 300,
        value: 78,
        config: {
          title: '内存使用率',
          unit: '%',
          max: 100
        }
      },
      {
        id: 'chart-1',
        type: 'line-chart',
        name: '网络流量趋势',
        x: 750,
        y: 120,
        width: 800,
        height: 400,
        data: [
          { name: '00:00', value: 120 },
          { name: '04:00', value: 80 },
          { name: '08:00', value: 200 },
          { name: '12:00', value: 350 },
          { name: '16:00', value: 280 },
          { name: '20:00', value: 180 }
        ],
        config: {
          title: '网络流量趋势 (MB/s)',
          colorScheme: 'gradient-green'
        }
      }
    ]
  },
  {
    id: 3,
    name: '电商数据分析',
    description: '展示电商平台的订单量、用户活跃度、商品销售排行等核心数据，助力电商运营决策。',
    category: 'ecommerce',
    categoryName: '电商零售',
    thumbnail: '/images/templates/ecommerce-analytics.jpg',
    width: 1920,
    height: 1080,
    tags: ['电商', '订单', '用户', '销售'],
    isNew: true,
    isHot: false,
    isPro: false,
    usageCount: 567,
    createTime: '2024-01-22 11:20:00',
    updateTime: '2024-01-22 11:20:00',
    author: 'DataV官方',
    components: []
  },
  {
    id: 4,
    name: '财务报表大屏',
    description: '展示企业财务状况、收支分析、利润趋势等财务数据，为财务决策提供数据支持。',
    category: 'finance',
    categoryName: '金融财务',
    thumbnail: '/images/templates/finance-report.jpg',
    width: 1920,
    height: 1080,
    tags: ['财务', '报表', '利润', '分析'],
    isNew: false,
    isHot: false,
    isPro: true,
    usageCount: 423,
    createTime: '2024-01-19 14:30:00',
    updateTime: '2024-01-20 16:45:00',
    author: 'DataV官方',
    components: []
  },
  {
    id: 5,
    name: '物流运输监控',
    description: '实时监控物流运输状态、配送进度、车辆位置等信息，提升物流管理效率。',
    category: 'logistics',
    categoryName: '物流运输',
    thumbnail: '/images/templates/logistics-monitor.jpg',
    width: 1920,
    height: 1080,
    tags: ['物流', '运输', '配送', '监控'],
    isNew: false,
    isHot: true,
    isPro: false,
    usageCount: 334,
    createTime: '2024-01-17 09:15:00',
    updateTime: '2024-01-19 13:20:00',
    author: 'DataV官方',
    components: []
  }
]

/**
 * 获取模板分类列表
 */
export const getTemplateCategories = () => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        code: 200,
        msg: 'success',
        data: mockCategories
      })
    }, 300)
  })
}

/**
 * 获取模板列表
 */
export const getTemplateList = (params = {}) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      let result = [...mockTemplates]
      
      // 分类筛选
      if (params.category && params.category !== 'all') {
        result = result.filter(template => template.category === params.category)
      }
      
      // 搜索筛选
      if (params.keyword) {
        const keyword = params.keyword.toLowerCase()
        result = result.filter(template => 
          template.name.toLowerCase().includes(keyword) ||
          template.description.toLowerCase().includes(keyword) ||
          template.tags.some(tag => tag.toLowerCase().includes(keyword))
        )
      }
      
      resolve({
        code: 200,
        msg: 'success',
        data: result
      })
    }, 500)
  })
}

/**
 * 获取模板详情
 */
export const getTemplateDetail = (templateId) => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const template = mockTemplates.find(t => t.id == templateId)
      if (template) {
        resolve({
          code: 200,
          msg: 'success',
          data: template
        })
      } else {
        reject({
          code: 404,
          msg: '模板不存在'
        })
      }
    }, 300)
  })
}

/**
 * 使用模板创建项目
 */
export const createProjectFromTemplate = (templateId, projectData) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const template = mockTemplates.find(t => t.id == templateId)
      if (template) {
        const newProject = {
          id: Date.now(),
          name: projectData.name || template.name,
          description: projectData.description || template.description,
          width: template.width,
          height: template.height,
          components: template.components || [],
          templateId: templateId,
          createTime: new Date().toISOString(),
          updateTime: new Date().toISOString()
        }
        
        resolve({
          code: 200,
          msg: 'success',
          data: newProject
        })
      } else {
        resolve({
          code: 404,
          msg: '模板不存在'
        })
      }
    }, 800)
  })
}

/**
 * 保存为模板
 */
export const saveAsTemplate = (templateData) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const newTemplate = {
        id: Date.now(),
        ...templateData,
        usageCount: 0,
        createTime: new Date().toISOString(),
        updateTime: new Date().toISOString(),
        author: '用户自定义'
      }
      
      mockTemplates.unshift(newTemplate)
      
      resolve({
        code: 200,
        msg: 'success',
        data: newTemplate
      })
    }, 1000)
  })
}

/**
 * 删除模板
 */
export const deleteTemplate = (templateId) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const index = mockTemplates.findIndex(t => t.id == templateId)
      if (index !== -1) {
        mockTemplates.splice(index, 1)
        resolve({
          code: 200,
          msg: 'success'
        })
      } else {
        resolve({
          code: 404,
          msg: '模板不存在'
        })
      }
    }, 500)
  })
}
