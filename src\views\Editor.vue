<template>
  <div class="editor-container">
    <!-- 顶部工具栏 -->
    <header class="editor-header">
      <div class="header-left">
        <el-button text @click="goBack">
          <el-icon><ArrowLeft /></el-icon>
          返回
        </el-button>
        <div class="project-info">
          <span class="project-name">{{ projectName }}</span>
          <span class="save-status">{{ saveStatus }}</span>
        </div>
      </div>
      <div class="header-center">
        <div class="toolbar">
          <el-button-group>
            <el-button :icon="Refresh" @click="resetCanvas">重置</el-button>
            <el-button :icon="ZoomOut" @click="zoomOut">缩小</el-button>
            <span class="zoom-level">{{ zoomLevel }}%</span>
            <el-button :icon="ZoomIn" @click="zoomIn">放大</el-button>
          </el-button-group>
        </div>
      </div>
      <div class="header-right">
        <el-button @click="previewProject">
          <el-icon><View /></el-icon>
          预览
        </el-button>
        <el-button @click="saveAsTemplate">
          <el-icon><Collection /></el-icon>
          保存为模板
        </el-button>
        <el-button type="primary" @click="saveProject">
          <el-icon><Document /></el-icon>
          保存
        </el-button>
        <el-button type="success" @click="publishProject">
          <el-icon><Upload /></el-icon>
          发布
        </el-button>
      </div>
    </header>

    <!-- 主要编辑区域 -->
    <div class="editor-main">
      <!-- 左侧组件库 -->
      <aside class="left-panel">
        <div class="panel-header">
          <h3>组件库</h3>
        </div>
        <div class="component-categories">
          <div
            v-for="category in componentCategories"
            :key="category.id"
            class="category-item"
            :class="{ active: activeCategory === category.id }"
            @click="selectCategory(category.id)"
          >
            <el-icon><component :is="category.icon" /></el-icon>
            <span>{{ category.name }}</span>
          </div>
        </div>
        <div class="component-list">
          <div
            v-for="component in currentComponents"
            :key="component.id"
            class="component-item"
            draggable="true"
            @dragstart="handleDragStart($event, component)"
          >
            <div class="component-thumbnail">
              <el-icon><component :is="component.icon" /></el-icon>
            </div>
            <span class="component-name">{{ component.name }}</span>
          </div>
        </div>
      </aside>

      <!-- 中间画布区域 -->
      <main class="canvas-area">
        <DragCanvas
          :width="canvasWidth"
          :height="canvasHeight"
          @component-select="handleComponentSelect"
          @component-update="handleComponentUpdate"
          @component-delete="handleComponentDelete"
        />
      </main>

      <!-- 右侧属性面板 -->
      <aside class="right-panel">
        <PropertyPanel />
      </aside>
    </div>

    <!-- 保存为模板对话框 -->
    <SaveAsTemplateDialog
      v-model="saveTemplateDialogVisible"
      :project="currentProject"
      @saved="handleTemplateSaved"
    />
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { getComponentCategories } from '@/api/componentApi'
import { getProjectDetail, updateProject } from '@/api/projectApi'
import { useStore } from '@/store'
import DragCanvas from '@/components/editor/DragCanvas.vue'
import PropertyPanel from '@/components/editor/PropertyPanel.vue'
import SaveAsTemplateDialog from '@/components/template/SaveAsTemplateDialog.vue'

const router = useRouter()
const route = useRoute()
const { state, actions } = useStore()

// 响应式数据
const projectName = ref('未命名项目')
const saveStatus = ref('已保存')
const componentCategories = ref([])
const activeCategory = ref('chart')
const saveTemplateDialogVisible = ref(false)
const currentComponents = ref([])
const canvasWidth = ref(1920)
const canvasHeight = ref(1080)

// 计算属性
const zoomLevel = computed(() => state.editor.zoom)
const selectedComponent = computed(() => state.editor.selectedComponent)

const currentProject = computed(() => ({
  name: projectName.value,
  description: '',
  width: state.currentProject.width,
  height: state.currentProject.height,
  components: state.currentProject.components
}))

// 方法
const loadComponentCategories = async () => {
  try {
    const res = await getComponentCategories()
    if (res.code === 200) {
      componentCategories.value = res.data
      if (res.data.length > 0) {
        selectCategory(res.data[0].id)
      }
    }
  } catch (error) {
    ElMessage.error('加载组件库失败')
  }
}

const selectCategory = (categoryId) => {
  activeCategory.value = categoryId
  const category = componentCategories.value.find(cat => cat.id === categoryId)
  if (category) {
    currentComponents.value = category.components
  }
}

const handleDragStart = (event, component) => {
  event.dataTransfer.setData('component', JSON.stringify(component))
}

const handleComponentSelect = (component) => {
  // 组件选择事件处理
}

const handleComponentUpdate = (componentId, updates) => {
  // 组件更新事件处理
  saveStatus.value = '未保存'
}

const handleComponentDelete = (componentId) => {
  // 组件删除事件处理
  saveStatus.value = '未保存'
}

const goBack = () => {
  router.push('/projects')
}

const zoomIn = () => {
  actions.setZoom(Math.min(500, zoomLevel.value + 10))
}

const zoomOut = () => {
  actions.setZoom(Math.max(10, zoomLevel.value - 10))
}

const resetCanvas = () => {
  actions.setZoom(100)
}

const previewProject = () => {
  const projectId = route.params.id
  if (projectId) {
    router.push(`/preview/${projectId}`)
  }
}

const saveProject = () => {
  // 保存项目逻辑
  saveStatus.value = '保存中...'
  setTimeout(() => {
    saveStatus.value = '已保存'
    ElMessage.success('项目保存成功')
  }, 1000)
}

const saveAsTemplate = () => {
  saveTemplateDialogVisible.value = true
}

const handleTemplateSaved = (template) => {
  ElMessage.success('模板保存成功')
  // 可以跳转到模板中心查看
}

const publishProject = () => {
  ElMessage.success('项目发布成功')
}

// 生命周期
onMounted(() => {
  loadComponentCategories()
  
  // 如果有项目ID，加载项目数据
  const projectId = route.params.id
  if (projectId) {
    // 这里可以加载具体的项目数据
    projectName.value = '智慧城市大屏'
  }
})
</script>

<style scoped>
.editor-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #1a1a2e;
  color: #ffffff;
}

.editor-header {
  height: 60px;
  background: #16213e;
  border-bottom: 1px solid #2a3f5f;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 1rem;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.project-info {
  display: flex;
  flex-direction: column;
}

.project-name {
  font-weight: 600;
  font-size: 0.9rem;
}

.save-status {
  font-size: 0.7rem;
  color: #999;
}

.header-center {
  flex: 1;
  display: flex;
  justify-content: center;
}

.toolbar {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.zoom-level {
  padding: 0 1rem;
  font-size: 0.9rem;
  color: #999;
}

.header-right {
  display: flex;
  gap: 0.5rem;
}

.editor-main {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.left-panel, .right-panel {
  width: 300px;
  background: #16213e;
  border-right: 1px solid #2a3f5f;
  display: flex;
  flex-direction: column;
}

.right-panel {
  border-right: none;
  border-left: 1px solid #2a3f5f;
}

.panel-header {
  height: 50px;
  display: flex;
  align-items: center;
  padding: 0 1rem;
  border-bottom: 1px solid #2a3f5f;
}

.panel-header h3 {
  margin: 0;
  font-size: 1rem;
}

.component-categories {
  border-bottom: 1px solid #2a3f5f;
}

.category-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.8rem 1rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.category-item:hover {
  background: rgba(255, 255, 255, 0.05);
}

.category-item.active {
  background: rgba(0, 245, 255, 0.1);
  color: #00f5ff;
}

.component-list {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
}

.component-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.8rem;
  margin-bottom: 0.5rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 6px;
  cursor: grab;
  transition: all 0.2s;
}

.component-item:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateX(5px);
}

.component-item:active {
  cursor: grabbing;
}

.component-thumbnail {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #00f5ff;
}

.component-name {
  font-size: 0.9rem;
}

.canvas-area {
  flex: 1;
  background: #0f1419;
  overflow: auto;
  position: relative;
}

.canvas-container {
  padding: 2rem;
  min-height: 100%;
  display: flex;
  justify-content: center;
  align-items: flex-start;
}

.canvas {
  background: #1a1a2e;
  border: 2px solid #2a3f5f;
  position: relative;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
}

.canvas-grid {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    linear-gradient(rgba(255, 255, 255, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255, 255, 255, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
  pointer-events: none;
}

.canvas-component {
  border: 1px solid transparent;
  cursor: move;
}

.canvas-component.selected {
  border-color: #00f5ff;
  box-shadow: 0 0 10px rgba(0, 245, 255, 0.3);
}

.component-placeholder {
  width: 100%;
  height: 100%;
  background: rgba(0, 245, 255, 0.1);
  border: 1px dashed rgba(0, 245, 255, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
  color: #00f5ff;
}

.resize-handles {
  position: absolute;
  top: -4px;
  left: -4px;
  right: -4px;
  bottom: -4px;
  pointer-events: none;
}

.resize-handle {
  position: absolute;
  width: 8px;
  height: 8px;
  background: #00f5ff;
  border: 1px solid #ffffff;
  pointer-events: all;
  cursor: nw-resize;
}

.resize-handle.nw { top: 0; left: 0; }
.resize-handle.ne { top: 0; right: 0; cursor: ne-resize; }
.resize-handle.sw { bottom: 0; left: 0; cursor: sw-resize; }
.resize-handle.se { bottom: 0; right: 0; cursor: se-resize; }

.property-content {
  flex: 1;
  padding: 1rem;
}

.no-selection {
  text-align: center;
  color: #999;
  padding: 2rem 0;
}

.no-selection p {
  margin-top: 1rem;
}

.component-properties {
  /* 属性面板样式 */
}
</style>
