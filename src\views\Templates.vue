<template>
  <div class="templates-container">
    <!-- 顶部导航 -->
    <header class="header">
      <div class="header-content">
        <div class="logo" @click="goHome">
          <img src="/favicon.ico" alt="DataV" class="logo-icon">
          <span class="logo-text">DataV</span>
        </div>
        <div class="header-actions">
          <el-button @click="goToProjects">
            <el-icon><Folder /></el-icon>
            我的项目
          </el-button>
          <el-button type="primary" @click="createCustomTemplate">
            <el-icon><Plus /></el-icon>
            创建模板
          </el-button>
        </div>
      </div>
    </header>

    <!-- 主要内容 -->
    <main class="main-content">
      <!-- 分类导航 -->
      <div class="category-nav">
        <div class="nav-container">
          <div
            v-for="category in categories"
            :key="category.id"
            class="category-item"
            :class="{ active: activeCategory === category.id }"
            @click="setActiveCategory(category.id)"
          >
            <el-icon size="20">
              <component :is="category.icon" />
            </el-icon>
            <span>{{ category.name }}</span>
            <span class="count">({{ category.count }})</span>
          </div>
        </div>
      </div>

      <!-- 工具栏 -->
      <div class="toolbar">
        <div class="toolbar-left">
          <h1 class="page-title">{{ getCurrentCategoryName() }}</h1>
          <span class="template-count">共 {{ filteredTemplates.length }} 个模板</span>
        </div>
        <div class="toolbar-right">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索模板..."
            style="width: 300px"
            @input="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
          <el-select v-model="sortBy" placeholder="排序方式" style="width: 120px" @change="handleSort">
            <el-option label="最新" value="newest" />
            <el-option label="最热" value="popular" />
            <el-option label="名称" value="name" />
          </el-select>
          <el-button-group>
            <el-button :type="viewMode === 'grid' ? 'primary' : ''" @click="setViewMode('grid')">
              <el-icon><Grid /></el-icon>
            </el-button>
            <el-button :type="viewMode === 'list' ? 'primary' : ''" @click="setViewMode('list')">
              <el-icon><Menu /></el-icon>
            </el-button>
          </el-button-group>
        </div>
      </div>

      <!-- 模板列表 -->
      <div class="templates-content" v-loading="loading">
        <!-- 网格视图 -->
        <div v-if="viewMode === 'grid'" class="templates-grid">
          <div
            v-for="template in filteredTemplates"
            :key="template.id"
            class="template-card"
            @click="previewTemplate(template)"
          >
            <div class="card-image">
              <img :src="template.thumbnail" :alt="template.name" />
              <div class="card-overlay">
                <div class="overlay-actions">
                  <el-button type="primary" @click.stop="useTemplate(template)">
                    <el-icon><Plus /></el-icon>
                    使用模板
                  </el-button>
                  <el-button @click.stop="previewTemplate(template)">
                    <el-icon><View /></el-icon>
                    预览
                  </el-button>
                </div>
              </div>
              <div class="card-badges">
                <span v-if="template.isNew" class="badge new">新</span>
                <span v-if="template.isHot" class="badge hot">热</span>
                <span v-if="template.isPro" class="badge pro">专业版</span>
              </div>
            </div>
            <div class="card-content">
              <h3 class="template-name">{{ template.name }}</h3>
              <p class="template-desc">{{ template.description }}</p>
              <div class="template-meta">
                <div class="meta-left">
                  <span class="category">{{ template.categoryName }}</span>
                  <span class="size">{{ template.width }}×{{ template.height }}</span>
                </div>
                <div class="meta-right">
                  <span class="usage">{{ template.usageCount }}次使用</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 列表视图 -->
        <div v-if="viewMode === 'list'" class="templates-list">
          <div
            v-for="template in filteredTemplates"
            :key="template.id"
            class="template-item"
            @click="previewTemplate(template)"
          >
            <div class="item-image">
              <img :src="template.thumbnail" :alt="template.name" />
            </div>
            <div class="item-content">
              <div class="item-header">
                <h3 class="template-name">{{ template.name }}</h3>
                <div class="item-badges">
                  <span v-if="template.isNew" class="badge new">新</span>
                  <span v-if="template.isHot" class="badge hot">热</span>
                  <span v-if="template.isPro" class="badge pro">专业版</span>
                </div>
              </div>
              <p class="template-desc">{{ template.description }}</p>
              <div class="item-meta">
                <span class="category">{{ template.categoryName }}</span>
                <span class="size">{{ template.width }}×{{ template.height }}</span>
                <span class="usage">{{ template.usageCount }}次使用</span>
                <span class="date">{{ formatDate(template.createTime) }}</span>
              </div>
            </div>
            <div class="item-actions">
              <el-button type="primary" @click.stop="useTemplate(template)">
                使用模板
              </el-button>
              <el-button @click.stop="previewTemplate(template)">
                预览
              </el-button>
            </div>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-if="filteredTemplates.length === 0 && !loading" class="empty-state">
          <el-icon size="80"><Document /></el-icon>
          <h3>暂无模板</h3>
          <p>该分类下暂时没有模板，请尝试其他分类或创建自定义模板</p>
          <el-button type="primary" @click="createCustomTemplate">
            <el-icon><Plus /></el-icon>
            创建模板
          </el-button>
        </div>
      </div>
    </main>

    <!-- 模板预览对话框 -->
    <TemplatePreviewDialog
      v-model="previewDialogVisible"
      :template="currentTemplate"
      @use="useTemplate"
    />

    <!-- 创建项目对话框 -->
    <CreateProjectDialog
      v-model="createDialogVisible"
      :template="selectedTemplate"
      @created="handleProjectCreated"
    />
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { getTemplateList, getTemplateCategories } from '@/api/templateApi'
import TemplatePreviewDialog from '@/components/template/TemplatePreviewDialog.vue'
import CreateProjectDialog from '@/components/template/CreateProjectDialog.vue'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const categories = ref([])
const templates = ref([])
const activeCategory = ref('all')
const searchKeyword = ref('')
const sortBy = ref('newest')
const viewMode = ref('grid')
const previewDialogVisible = ref(false)
const createDialogVisible = ref(false)
const currentTemplate = ref(null)
const selectedTemplate = ref(null)

// 计算属性
const filteredTemplates = computed(() => {
  let result = [...templates.value]
  
  // 分类筛选
  if (activeCategory.value !== 'all') {
    result = result.filter(template => template.category === activeCategory.value)
  }
  
  // 搜索过滤
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    result = result.filter(template => 
      template.name.toLowerCase().includes(keyword) ||
      template.description.toLowerCase().includes(keyword) ||
      template.tags.some(tag => tag.toLowerCase().includes(keyword))
    )
  }
  
  // 排序
  switch (sortBy.value) {
    case 'newest':
      result.sort((a, b) => new Date(b.createTime) - new Date(a.createTime))
      break
    case 'popular':
      result.sort((a, b) => b.usageCount - a.usageCount)
      break
    case 'name':
      result.sort((a, b) => a.name.localeCompare(b.name))
      break
  }
  
  return result
})

// 方法
const loadCategories = async () => {
  try {
    const res = await getTemplateCategories()
    if (res.code === 200) {
      categories.value = [
        { id: 'all', name: '全部', icon: 'Grid', count: 0 },
        ...res.data
      ]
      
      // 计算每个分类的模板数量
      updateCategoryCounts()
    }
  } catch (error) {
    ElMessage.error('加载分类失败')
  }
}

const loadTemplates = async () => {
  loading.value = true
  try {
    const res = await getTemplateList()
    if (res.code === 200) {
      templates.value = res.data
      updateCategoryCounts()
    }
  } catch (error) {
    ElMessage.error('加载模板失败')
  } finally {
    loading.value = false
  }
}

const updateCategoryCounts = () => {
  categories.value.forEach(category => {
    if (category.id === 'all') {
      category.count = templates.value.length
    } else {
      category.count = templates.value.filter(t => t.category === category.id).length
    }
  })
}

const setActiveCategory = (categoryId) => {
  activeCategory.value = categoryId
}

const getCurrentCategoryName = () => {
  const category = categories.value.find(c => c.id === activeCategory.value)
  return category ? category.name + '模板' : '模板'
}

const setViewMode = (mode) => {
  viewMode.value = mode
}

const handleSearch = () => {
  // 搜索逻辑已在计算属性中处理
}

const handleSort = () => {
  // 排序逻辑已在计算属性中处理
}

const previewTemplate = (template) => {
  currentTemplate.value = template
  previewDialogVisible.value = true
}

const useTemplate = (template) => {
  selectedTemplate.value = template
  createDialogVisible.value = true
}

const createCustomTemplate = () => {
  router.push('/editor')
}

const handleProjectCreated = (project) => {
  ElMessage.success('项目创建成功')
  router.push(`/editor/${project.id}`)
}

const goHome = () => {
  router.push('/')
}

const goToProjects = () => {
  router.push('/projects')
}

const formatDate = (dateString) => {
  return new Date(dateString).toLocaleDateString()
}

// 生命周期
onMounted(() => {
  loadCategories()
  loadTemplates()
})
</script>

<style scoped>
.templates-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #0a1a3a 0%, #1a3a8f 100%);
  color: #ffffff;
}

.header {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding: 0 2rem;
}

.header-content {
  max-width: 1400px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 70px;
}

.logo {
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
}

.logo-icon {
  width: 32px;
  height: 32px;
}

.logo-text {
  font-size: 20px;
  font-weight: 600;
  color: #ffffff;
}

.header-actions {
  display: flex;
  gap: 1rem;
}

.main-content {
  max-width: 1400px;
  margin: 0 auto;
  padding: 2rem;
}

.category-nav {
  margin-bottom: 2rem;
}

.nav-container {
  display: flex;
  gap: 1rem;
  overflow-x: auto;
  padding-bottom: 1rem;
}

.category-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 25px;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
  border: 1px solid transparent;
}

.category-item:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(0, 245, 255, 0.3);
}

.category-item.active {
  background: rgba(0, 245, 255, 0.2);
  border-color: #00f5ff;
  color: #00f5ff;
}

.count {
  font-size: 0.8rem;
  opacity: 0.7;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.page-title {
  font-size: 2rem;
  font-weight: 600;
  margin: 0;
}

.template-count {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.9rem;
}

.toolbar-right {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.templates-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 2rem;
}

.template-card {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  overflow: hidden;
}

.template-card:hover {
  transform: translateY(-5px);
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(0, 245, 255, 0.3);
}

.card-image {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.card-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.card-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.template-card:hover .card-overlay {
  opacity: 1;
}

.overlay-actions {
  display: flex;
  gap: 1rem;
}

.card-badges {
  position: absolute;
  top: 10px;
  right: 10px;
  display: flex;
  gap: 5px;
}

.badge {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 500;
}

.badge.new {
  background: #00ff88;
  color: #000;
}

.badge.hot {
  background: #ff4757;
  color: #fff;
}

.badge.pro {
  background: #ffa502;
  color: #fff;
}

.card-content {
  padding: 1.5rem;
}

.template-name {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
  color: #ffffff;
}

.template-desc {
  color: rgba(255, 255, 255, 0.7);
  margin: 0 0 1rem 0;
  font-size: 0.9rem;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.template-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.5);
}

.meta-left {
  display: flex;
  gap: 1rem;
}

.templates-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.template-item {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  padding: 1.5rem;
  display: flex;
  gap: 1.5rem;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.template-item:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(0, 245, 255, 0.3);
}

.item-image {
  width: 120px;
  height: 80px;
  border-radius: 8px;
  overflow: hidden;
  flex-shrink: 0;
}

.item-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.item-content {
  flex: 1;
}

.item-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 0.5rem;
}

.item-badges {
  display: flex;
  gap: 5px;
}

.item-meta {
  display: flex;
  gap: 1rem;
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.5);
}

.item-actions {
  display: flex;
  gap: 1rem;
  flex-shrink: 0;
}

.empty-state {
  text-align: center;
  padding: 4rem 2rem;
  color: rgba(255, 255, 255, 0.7);
}

.empty-state h3 {
  margin: 1rem 0;
  color: #ffffff;
}

@media (max-width: 768px) {
  .toolbar {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }
  
  .toolbar-right {
    flex-direction: column;
  }
  
  .templates-grid {
    grid-template-columns: 1fr;
  }
  
  .template-item {
    flex-direction: column;
    text-align: center;
  }
  
  .item-image {
    width: 100%;
    height: 150px;
  }
}
</style>
