<template>
  <div class="chart-component" ref="chartContainer"></div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import * as echarts from 'echarts'

const props = defineProps({
  config: { type: Object, required: true },
  data: { type: Object, default: () => ({}) }
})

const chartContainer = ref(null)
let chartInstance = null

// 图表配置
const getChartOption = () => {
  const { type, config: chartConfig } = props.config
  const data = props.data.data || props.config.data || []

  const baseOption = {
    backgroundColor: 'transparent',
    textStyle: {
      color: '#ffffff'
    },
    grid: {
      left: chartConfig?.gridLeft || '10%',
      right: chartConfig?.gridRight || '10%',
      top: chartConfig?.gridTop || '15%',
      bottom: chartConfig?.gridBottom || '15%',
      containLabel: true
    },
    animation: chartConfig?.animation !== false,
    animationDuration: chartConfig?.animationDuration || 1000,
    animationEasing: chartConfig?.animationEasing || 'cubicOut'
  }
  
  switch (type) {
    case 'bar-chart':
      return {
        ...baseOption,
        title: {
          text: chartConfig?.title || '',
          textStyle: { color: '#ffffff', fontSize: 14 },
          left: 'center'
        },
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          borderColor: '#00f5ff',
          textStyle: { color: '#ffffff' }
        },
        xAxis: {
          type: 'category',
          data: data.map(item => item.name),
          axisLine: { lineStyle: { color: '#2a3f5f' } },
          axisLabel: { color: '#999' }
        },
        yAxis: {
          type: 'value',
          axisLine: { lineStyle: { color: '#2a3f5f' } },
          axisLabel: { color: '#999' },
          splitLine: { lineStyle: { color: '#2a3f5f' } }
        },
        series: [{
          type: 'bar',
          data: data.map(item => item.value),
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#00f5ff' },
              { offset: 1, color: '#1a3a8f' }
            ])
          },
          emphasis: {
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#00ffa3' },
                { offset: 1, color: '#00f5ff' }
              ])
            }
          }
        }]
      }
      
    case 'line-chart':
      return {
        ...baseOption,
        title: {
          text: chartConfig?.title || '',
          textStyle: { color: '#ffffff', fontSize: 14 },
          left: 'center'
        },
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          borderColor: '#00f5ff',
          textStyle: { color: '#ffffff' }
        },
        xAxis: {
          type: 'category',
          data: data.map(item => item.name),
          axisLine: { lineStyle: { color: '#2a3f5f' } },
          axisLabel: { color: '#999' }
        },
        yAxis: {
          type: 'value',
          axisLine: { lineStyle: { color: '#2a3f5f' } },
          axisLabel: { color: '#999' },
          splitLine: { lineStyle: { color: '#2a3f5f' } }
        },
        series: [{
          type: 'line',
          data: data.map(item => item.value),
          lineStyle: { color: '#00f5ff', width: 2 },
          itemStyle: { color: '#00f5ff' },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: 'rgba(0, 245, 255, 0.3)' },
              { offset: 1, color: 'rgba(0, 245, 255, 0.1)' }
            ])
          },
          smooth: true
        }]
      }
      
    case 'pie-chart':
      return {
        ...baseOption,
        title: {
          text: chartConfig?.title || '',
          textStyle: { color: '#ffffff', fontSize: 14 },
          left: 'center',
          top: '5%'
        },
        tooltip: {
          trigger: 'item',
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          borderColor: '#00f5ff',
          textStyle: { color: '#ffffff' },
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          left: 'left',
          top: 'middle',
          textStyle: { color: '#999', fontSize: 10 }
        },
        series: [{
          name: chartConfig?.title || '数据',
          type: 'pie',
          radius: ['30%', '70%'],
          center: ['60%', '50%'],
          data: data.map((item, index) => ({
            ...item,
            itemStyle: {
              color: getColorByIndex(index)
            }
          })),
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 245, 255, 0.5)'
            }
          },
          label: {
            color: '#ffffff',
            fontSize: 10
          }
        }]
      }

    case 'scatter-chart':
      return {
        ...baseOption,
        title: {
          text: chartConfig?.title || '',
          textStyle: { color: '#ffffff', fontSize: 14 },
          left: 'center'
        },
        tooltip: {
          trigger: 'item',
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          borderColor: '#00f5ff',
          textStyle: { color: '#ffffff' }
        },
        xAxis: {
          type: 'value',
          axisLine: { lineStyle: { color: '#2a3f5f' } },
          axisLabel: { color: '#999' },
          splitLine: { lineStyle: { color: '#2a3f5f' } }
        },
        yAxis: {
          type: 'value',
          axisLine: { lineStyle: { color: '#2a3f5f' } },
          axisLabel: { color: '#999' },
          splitLine: { lineStyle: { color: '#2a3f5f' } }
        },
        series: [{
          type: 'scatter',
          data: data.map(item => [item.x || item.value, item.y || item.value2 || Math.random() * 100]),
          itemStyle: {
            color: '#00f5ff',
            shadowBlur: 10,
            shadowColor: 'rgba(0, 245, 255, 0.5)'
          },
          symbolSize: 8
        }]
      }

    case 'radar-chart':
      return {
        ...baseOption,
        title: {
          text: chartConfig?.title || '',
          textStyle: { color: '#ffffff', fontSize: 14 },
          left: 'center'
        },
        tooltip: {
          trigger: 'item',
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          borderColor: '#00f5ff',
          textStyle: { color: '#ffffff' }
        },
        radar: {
          indicator: data.map(item => ({
            name: item.name,
            max: item.max || 100
          })),
          axisLine: { lineStyle: { color: '#2a3f5f' } },
          splitLine: { lineStyle: { color: '#2a3f5f' } },
          axisLabel: { color: '#999' },
          name: { textStyle: { color: '#ffffff' } }
        },
        series: [{
          type: 'radar',
          data: [{
            value: data.map(item => item.value),
            name: chartConfig?.seriesName || '数据',
            itemStyle: { color: '#00f5ff' },
            areaStyle: {
              color: new echarts.graphic.RadialGradient(0.5, 0.5, 1, [
                { offset: 0, color: 'rgba(0, 245, 255, 0.3)' },
                { offset: 1, color: 'rgba(0, 245, 255, 0.1)' }
              ])
            }
          }]
        }]
      }

    default:
      return {
        ...baseOption,
        title: {
          text: '暂不支持的图表类型',
          textStyle: { color: '#999' },
          left: 'center',
          top: 'middle'
        }
      }
  }
}

// 根据索引获取颜色
const getColorByIndex = (index) => {
  const colors = [
    '#00f5ff', '#1a3a8f', '#00ffa3', '#ffaa33', 
    '#ff3e3e', '#8b5cf6', '#06d6a0', '#f72585'
  ]
  return colors[index % colors.length]
}

// 初始化图表
const initChart = () => {
  if (!chartContainer.value) return
  
  chartInstance = echarts.init(chartContainer.value)
  updateChart()
}

// 更新图表
const updateChart = () => {
  if (!chartInstance) return
  
  const option = getChartOption()
  chartInstance.setOption(option, true)
}

// 调整图表大小
const resizeChart = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

// 监听配置变化
watch(() => props.config, () => {
  updateChart()
}, { deep: true })

watch(() => props.data, () => {
  updateChart()
}, { deep: true })

// 生命周期
onMounted(() => {
  nextTick(() => {
    initChart()
  })
  
  // 监听窗口大小变化
  window.addEventListener('resize', resizeChart)
})

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
    chartInstance = null
  }
  window.removeEventListener('resize', resizeChart)
})

// 暴露方法给父组件
defineExpose({
  resizeChart,
  updateChart
})
</script>

<style scoped>
.chart-component {
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}
</style>
