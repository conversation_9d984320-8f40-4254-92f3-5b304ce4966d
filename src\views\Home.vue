<template>
  <div class="home-container">
    <!-- 顶部导航 -->
    <header class="header">
      <div class="header-content">
        <div class="logo">
          <img src="/favicon.ico" alt="DataV" class="logo-icon">
          <span class="logo-text">DataV可视化大屏构建系统</span>
        </div>
        <nav class="nav">
          <el-button type="primary" @click="goToProjects">
            <el-icon><Folder /></el-icon>
            我的项目
          </el-button>
          <el-button @click="createNewProject">
            <el-icon><Plus /></el-icon>
            新建项目
          </el-button>
        </nav>
      </div>
    </header>

    <!-- 主要内容区 -->
    <main class="main-content">
      <!-- 欢迎区域 -->
      <section class="welcome-section">
        <div class="welcome-content">
          <h1 class="welcome-title">欢迎使用DataV可视化大屏构建系统</h1>
          <p class="welcome-desc">
            专业的数据可视化大屏制作平台，提供丰富的图表组件和灵活的布局工具，
            让您轻松创建炫酷的数据大屏展示。
          </p>
          <div class="welcome-actions">
            <el-button type="primary" size="large" @click="createNewProject">
              <el-icon><Plus /></el-icon>
              立即开始创建
            </el-button>
            <el-button size="large" @click="goToProjects">
              <el-icon><View /></el-icon>
              查看示例
            </el-button>
            <el-button size="large" @click="goToDataSource">
              <el-icon><DataBoard /></el-icon>
              数据源管理
            </el-button>
            <el-button size="large" @click="goToTemplates">
              <el-icon><Grid /></el-icon>
              模板中心
            </el-button>
          </div>
        </div>
        <div class="welcome-image">
          <div class="demo-screen">
            <div class="screen-content">
              <div class="demo-chart"></div>
              <div class="demo-chart"></div>
              <div class="demo-chart"></div>
              <div class="demo-chart"></div>
            </div>
          </div>
        </div>
      </section>

      <!-- 特性介绍 -->
      <section class="features-section">
        <h2 class="section-title">核心特性</h2>
        <div class="features-grid">
          <div class="feature-card">
            <div class="feature-icon">
              <el-icon size="40"><TrendCharts /></el-icon>
            </div>
            <h3>丰富的图表组件</h3>
            <p>提供柱状图、折线图、饼图、地图等多种图表类型，满足各种数据展示需求</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">
              <el-icon size="40"><Edit /></el-icon>
            </div>
            <h3>拖拽式编辑</h3>
            <p>直观的拖拽操作，所见即所得的编辑体验，快速构建专业的数据大屏</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">
              <el-icon size="40"><DataBoard /></el-icon>
            </div>
            <h3>实时数据绑定</h3>
            <p>支持多种数据源接入，实时更新数据，让您的大屏始终展示最新信息</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">
              <el-icon size="40"><Monitor /></el-icon>
            </div>
            <h3>多屏适配</h3>
            <p>响应式设计，支持各种屏幕尺寸，确保在不同设备上都有完美的展示效果</p>
          </div>
        </div>
      </section>
    </main>

    <!-- 页脚 -->
    <footer class="footer">
      <p>&copy; 2024 DataV可视化大屏构建系统. All rights reserved.</p>
    </footer>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'

const router = useRouter()

// 跳转到项目列表
const goToProjects = () => {
  router.push('/projects')
}

// 跳转到数据源管理
const goToDataSource = () => {
  router.push('/datasource')
}

// 跳转到模板中心
const goToTemplates = () => {
  router.push('/templates')
}

// 创建新项目
const createNewProject = () => {
  router.push('/editor')
}
</script>

<style scoped>
.home-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #0a1a3a 0%, #1a3a8f 100%);
  color: #ffffff;
}

.header {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding: 0 2rem;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 70px;
}

.logo {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo-icon {
  width: 32px;
  height: 32px;
}

.logo-text {
  font-size: 20px;
  font-weight: 600;
  color: #ffffff;
}

.nav {
  display: flex;
  gap: 16px;
}

.main-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.welcome-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
  min-height: 500px;
  padding: 4rem 0;
}

.welcome-title {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  background: linear-gradient(45deg, #00f5ff, #ffffff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.welcome-desc {
  font-size: 1.2rem;
  line-height: 1.8;
  margin-bottom: 2rem;
  color: rgba(255, 255, 255, 0.8);
}

.welcome-actions {
  display: flex;
  gap: 1rem;
}

.demo-screen {
  background: rgba(0, 0, 0, 0.3);
  border-radius: 12px;
  padding: 2rem;
  border: 2px solid rgba(0, 245, 255, 0.3);
  box-shadow: 0 0 30px rgba(0, 245, 255, 0.2);
}

.screen-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.demo-chart {
  height: 100px;
  background: linear-gradient(45deg, rgba(0, 245, 255, 0.2), rgba(26, 58, 143, 0.2));
  border-radius: 8px;
  border: 1px solid rgba(0, 245, 255, 0.3);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 0.6; }
  50% { opacity: 1; }
}

.features-section {
  padding: 4rem 0;
}

.section-title {
  text-align: center;
  font-size: 2.5rem;
  font-weight: 600;
  margin-bottom: 3rem;
  color: #00f5ff;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.feature-card {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  padding: 2rem;
  text-align: center;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-5px);
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(0, 245, 255, 0.3);
}

.feature-icon {
  color: #00f5ff;
  margin-bottom: 1rem;
}

.feature-card h3 {
  font-size: 1.3rem;
  margin-bottom: 1rem;
  color: #ffffff;
}

.feature-card p {
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.6;
}

.footer {
  text-align: center;
  padding: 2rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.6);
}

@media (max-width: 768px) {
  .welcome-section {
    grid-template-columns: 1fr;
    text-align: center;
  }
  
  .welcome-title {
    font-size: 2rem;
  }
  
  .features-grid {
    grid-template-columns: 1fr;
  }
}
</style>
