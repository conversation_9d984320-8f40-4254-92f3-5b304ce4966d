<template>
  <div
    class="draggable-component"
    :class="{
      selected: selected,
      locked: component.locked,
      hidden: !component.visible
    }"
    :style="componentStyle"
    @mousedown="handleMouseDown"
    @click.stop="handleClick"
    @dblclick="handleDoubleClick"
  >
    <!-- 组件内容 -->
    <div class="component-content" :style="contentStyle">
      <component
        :is="getComponentType(component.type)"
        :config="component"
        :data="componentData"
      />
    </div>

    <!-- 选中状态的控制框 -->
    <div v-if="selected && !component.locked" class="selection-controls">
      <!-- 边框 -->
      <div class="selection-border"></div>
      
      <!-- 缩放控制点 -->
      <div
        v-for="handle in resizeHandles"
        :key="handle.position"
        class="resize-handle"
        :class="handle.position"
        :style="{ cursor: handle.cursor }"
        @mousedown.stop="handleResizeStart($event, handle.position)"
      ></div>
      
      <!-- 旋转控制点 -->
      <div
        class="rotate-handle"
        @mousedown.stop="handleRotateStart"
      >
        <el-icon><Refresh /></el-icon>
      </div>
      
      <!-- 组件信息 -->
      <div class="component-info">
        <span class="component-name">{{ component.name }}</span>
        <span class="component-size">{{ component.width }}×{{ component.height }}</span>
      </div>
    </div>

    <!-- 锁定状态指示器 -->
    <div v-if="component.locked" class="lock-indicator">
      <el-icon><Lock /></el-icon>
    </div>

    <!-- 右键菜单 -->
    <el-dropdown
      v-if="showContextMenu"
      :style="contextMenuStyle"
      trigger="manual"
      :visible="showContextMenu"
      @command="handleContextMenuCommand"
    >
      <div></div>
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item command="copy">
            <el-icon><CopyDocument /></el-icon>
            复制
          </el-dropdown-item>
          <el-dropdown-item command="paste" :disabled="!hasClipboard">
            <el-icon><Document /></el-icon>
            粘贴
          </el-dropdown-item>
          <el-dropdown-item command="duplicate">
            <el-icon><CopyDocument /></el-icon>
            复制并粘贴
          </el-dropdown-item>
          <el-dropdown-item divided command="lock" v-if="!component.locked">
            <el-icon><Lock /></el-icon>
            锁定
          </el-dropdown-item>
          <el-dropdown-item command="unlock" v-if="component.locked">
            <el-icon><Unlock /></el-icon>
            解锁
          </el-dropdown-item>
          <el-dropdown-item command="hide" v-if="component.visible">
            <el-icon><Hide /></el-icon>
            隐藏
          </el-dropdown-item>
          <el-dropdown-item command="show" v-if="!component.visible">
            <el-icon><View /></el-icon>
            显示
          </el-dropdown-item>
          <el-dropdown-item divided command="toTop">
            <el-icon><Top /></el-icon>
            置于顶层
          </el-dropdown-item>
          <el-dropdown-item command="toBottom">
            <el-icon><Bottom /></el-icon>
            置于底层
          </el-dropdown-item>
          <el-dropdown-item command="moveUp">
            <el-icon><ArrowUp /></el-icon>
            上移一层
          </el-dropdown-item>
          <el-dropdown-item command="moveDown">
            <el-icon><ArrowDown /></el-icon>
            下移一层
          </el-dropdown-item>
          <el-dropdown-item divided command="delete">
            <el-icon style="color: #f56c6c"><Delete /></el-icon>
            删除
          </el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { useStore } from '@/store'
import TextComponent from '@/components/widgets/TextComponent.vue'
import ChartComponent from '@/components/widgets/ChartComponent.vue'
import ImageComponent from '@/components/widgets/ImageComponent.vue'
import GaugeComponent from '@/components/widgets/GaugeComponent.vue'
import ProgressComponent from '@/components/widgets/ProgressComponent.vue'
import CounterComponent from '@/components/widgets/CounterComponent.vue'
import TableComponent from '@/components/widgets/TableComponent.vue'
import DefaultComponent from '@/components/widgets/DefaultComponent.vue'

const props = defineProps({
  component: { type: Object, required: true },
  selected: { type: Boolean, default: false },
  zoom: { type: Number, default: 100 }
})

const emit = defineEmits(['select', 'update', 'delete'])

const { state, actions, getters } = useStore()

// 响应式数据
const isDragging = ref(false)
const isResizing = ref(false)
const isRotating = ref(false)
const dragStart = reactive({ x: 0, y: 0 })
const resizeStart = reactive({ x: 0, y: 0, width: 0, height: 0 })
const resizeDirection = ref('')
const showContextMenu = ref(false)
const contextMenuPosition = reactive({ x: 0, y: 0 })

// 缩放控制点配置
const resizeHandles = [
  { position: 'nw', cursor: 'nw-resize' },
  { position: 'n', cursor: 'n-resize' },
  { position: 'ne', cursor: 'ne-resize' },
  { position: 'e', cursor: 'e-resize' },
  { position: 'se', cursor: 'se-resize' },
  { position: 's', cursor: 's-resize' },
  { position: 'sw', cursor: 'sw-resize' },
  { position: 'w', cursor: 'w-resize' }
]

// 计算属性
const componentStyle = computed(() => ({
  position: 'absolute',
  left: props.component.x + 'px',
  top: props.component.y + 'px',
  width: props.component.width + 'px',
  height: props.component.height + 'px',
  zIndex: props.component.zIndex,
  transform: `rotate(${props.component.rotation || 0}deg)`,
  opacity: props.component.visible ? 1 : 0.3,
  pointerEvents: props.component.locked ? 'none' : 'auto'
}))

const contentStyle = computed(() => ({
  width: '100%',
  height: '100%',
  overflow: 'hidden'
}))

const contextMenuStyle = computed(() => ({
  position: 'fixed',
  left: contextMenuPosition.x + 'px',
  top: contextMenuPosition.y + 'px',
  zIndex: 9999
}))

const hasClipboard = computed(() => getters.hasClipboard())
const componentData = computed(() => {
  // 这里可以根据组件ID获取实时数据
  return props.component.data || {}
})

// 方法
const getComponentType = (type) => {
  // 根据组件类型返回对应的组件
  const componentMap = {
    'text-title': TextComponent,
    'text-content': TextComponent,
    'bar-chart': ChartComponent,
    'line-chart': ChartComponent,
    'pie-chart': ChartComponent,
    'scatter-chart': ChartComponent,
    'radar-chart': ChartComponent,
    'gauge': GaugeComponent,
    'progress': ProgressComponent,
    'counter': CounterComponent,
    'table': TableComponent,
    'image': ImageComponent,
    'video': ImageComponent
  }
  return componentMap[type] || DefaultComponent
}

const handleClick = (event) => {
  if (!props.component.locked) {
    emit('select', props.component)
  }
}

const handleDoubleClick = (event) => {
  if (!props.component.locked) {
    // 双击进入编辑模式
    console.log('Double click to edit:', props.component.name)
  }
}

const handleMouseDown = (event) => {
  if (props.component.locked) return
  
  if (event.button === 0) { // 左键
    isDragging.value = true
    dragStart.x = event.clientX - props.component.x * (props.zoom / 100)
    dragStart.y = event.clientY - props.component.y * (props.zoom / 100)
    
    document.addEventListener('mousemove', handleDragMove)
    document.addEventListener('mouseup', handleDragEnd)
    
    event.preventDefault()
  } else if (event.button === 2) { // 右键
    showContextMenu.value = true
    contextMenuPosition.x = event.clientX
    contextMenuPosition.y = event.clientY
    
    // 延迟隐藏菜单
    setTimeout(() => {
      document.addEventListener('click', hideContextMenu)
    }, 100)
  }
}

const handleDragMove = (event) => {
  if (!isDragging.value) return
  
  const newX = (event.clientX - dragStart.x) / (props.zoom / 100)
  const newY = (event.clientY - dragStart.y) / (props.zoom / 100)
  
  emit('update', props.component.id, {
    x: Math.max(0, newX),
    y: Math.max(0, newY)
  })
}

const handleDragEnd = () => {
  isDragging.value = false
  document.removeEventListener('mousemove', handleDragMove)
  document.removeEventListener('mouseup', handleDragEnd)
}

const handleResizeStart = (event, direction) => {
  isResizing.value = true
  resizeDirection.value = direction
  resizeStart.x = event.clientX
  resizeStart.y = event.clientY
  resizeStart.width = props.component.width
  resizeStart.height = props.component.height
  
  document.addEventListener('mousemove', handleResizeMove)
  document.addEventListener('mouseup', handleResizeEnd)
  
  event.preventDefault()
}

const handleResizeMove = (event) => {
  if (!isResizing.value) return
  
  const deltaX = (event.clientX - resizeStart.x) / (props.zoom / 100)
  const deltaY = (event.clientY - resizeStart.y) / (props.zoom / 100)
  
  let newWidth = resizeStart.width
  let newHeight = resizeStart.height
  let newX = props.component.x
  let newY = props.component.y
  
  const direction = resizeDirection.value
  
  if (direction.includes('e')) {
    newWidth = Math.max(20, resizeStart.width + deltaX)
  }
  if (direction.includes('w')) {
    newWidth = Math.max(20, resizeStart.width - deltaX)
    newX = props.component.x + (resizeStart.width - newWidth)
  }
  if (direction.includes('s')) {
    newHeight = Math.max(20, resizeStart.height + deltaY)
  }
  if (direction.includes('n')) {
    newHeight = Math.max(20, resizeStart.height - deltaY)
    newY = props.component.y + (resizeStart.height - newHeight)
  }
  
  emit('update', props.component.id, {
    x: Math.max(0, newX),
    y: Math.max(0, newY),
    width: newWidth,
    height: newHeight
  })
}

const handleResizeEnd = () => {
  isResizing.value = false
  resizeDirection.value = ''
  document.removeEventListener('mousemove', handleResizeMove)
  document.removeEventListener('mouseup', handleResizeEnd)
}

const handleRotateStart = (event) => {
  isRotating.value = true
  // 旋转功能实现
  console.log('Rotate start')
}

const handleContextMenuCommand = (command) => {
  showContextMenu.value = false
  
  switch (command) {
    case 'copy':
      actions.copyComponent(props.component)
      break
    case 'paste':
      const newComponent = actions.pasteComponent()
      if (newComponent) {
        emit('select', newComponent)
      }
      break
    case 'duplicate':
      actions.copyComponent(props.component)
      const duplicated = actions.pasteComponent()
      if (duplicated) {
        emit('select', duplicated)
      }
      break
    case 'lock':
      emit('update', props.component.id, { locked: true })
      break
    case 'unlock':
      emit('update', props.component.id, { locked: false })
      break
    case 'hide':
      emit('update', props.component.id, { visible: false })
      break
    case 'show':
      emit('update', props.component.id, { visible: true })
      break
    case 'toTop':
      emit('update', props.component.id, { zIndex: 9999 })
      break
    case 'toBottom':
      emit('update', props.component.id, { zIndex: 0 })
      break
    case 'moveUp':
      emit('update', props.component.id, { zIndex: props.component.zIndex + 1 })
      break
    case 'moveDown':
      emit('update', props.component.id, { zIndex: Math.max(0, props.component.zIndex - 1) })
      break
    case 'delete':
      emit('delete', props.component.id)
      break
  }
}

const hideContextMenu = () => {
  showContextMenu.value = false
  document.removeEventListener('click', hideContextMenu)
}

// 生命周期
onMounted(() => {
  // 禁用右键菜单
  document.addEventListener('contextmenu', (e) => {
    if (e.target.closest('.draggable-component')) {
      e.preventDefault()
    }
  })
})

onUnmounted(() => {
  document.removeEventListener('mousemove', handleDragMove)
  document.removeEventListener('mouseup', handleDragEnd)
  document.removeEventListener('mousemove', handleResizeMove)
  document.removeEventListener('mouseup', handleResizeEnd)
  document.removeEventListener('click', hideContextMenu)
})
</script>

<style scoped>
.draggable-component {
  cursor: move;
  user-select: none;
}

.draggable-component.locked {
  cursor: not-allowed;
}

.draggable-component.hidden {
  opacity: 0.3;
}

.component-content {
  border-radius: 4px;
  overflow: hidden;
}

.selection-controls {
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  pointer-events: none;
}

.selection-border {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border: 2px solid #00f5ff;
  border-radius: 4px;
  box-shadow: 0 0 10px rgba(0, 245, 255, 0.3);
}

.resize-handle {
  position: absolute;
  width: 8px;
  height: 8px;
  background: #00f5ff;
  border: 1px solid #ffffff;
  border-radius: 2px;
  pointer-events: all;
}

.resize-handle.nw { top: -4px; left: -4px; }
.resize-handle.n { top: -4px; left: 50%; transform: translateX(-50%); }
.resize-handle.ne { top: -4px; right: -4px; }
.resize-handle.e { top: 50%; right: -4px; transform: translateY(-50%); }
.resize-handle.se { bottom: -4px; right: -4px; }
.resize-handle.s { bottom: -4px; left: 50%; transform: translateX(-50%); }
.resize-handle.sw { bottom: -4px; left: -4px; }
.resize-handle.w { top: 50%; left: -4px; transform: translateY(-50%); }

.rotate-handle {
  position: absolute;
  top: -30px;
  left: 50%;
  transform: translateX(-50%);
  width: 20px;
  height: 20px;
  background: #00f5ff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: grab;
  pointer-events: all;
  color: #ffffff;
  font-size: 12px;
}

.rotate-handle:active {
  cursor: grabbing;
}

.component-info {
  position: absolute;
  top: -40px;
  right: 0;
  background: rgba(0, 0, 0, 0.8);
  color: #ffffff;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  display: flex;
  gap: 8px;
}

.component-name {
  color: #00f5ff;
}

.component-size {
  color: #999;
}

.lock-indicator {
  position: absolute;
  top: 5px;
  right: 5px;
  width: 20px;
  height: 20px;
  background: rgba(255, 170, 51, 0.9);
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  font-size: 12px;
}
</style>
