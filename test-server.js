// 简单的测试服务器
import { createServer } from 'vite'

async function startServer() {
  try {
    const server = await createServer({
      // 任何有效的用户配置选项，加上 `mode` 和 `configFile`
      configFile: './vite.config.js',
      root: process.cwd(),
      server: {
        port: 5177
      }
    })
    
    await server.listen()
    
    server.printUrls()
    console.log('开发服务器启动成功！')
  } catch (error) {
    console.error('启动服务器失败:', error)
  }
}

startServer()
