<template>
  <div class="image-style-panel">
    <div class="property-section">
      <h4>图片设置</h4>
      <el-form label-width="80px" size="small">
        <el-form-item label="图片地址">
          <el-input 
            v-model="styleData.src" 
            placeholder="请输入图片URL"
            @change="updateStyle"
          />
        </el-form-item>
        <el-form-item label="替代文字">
          <el-input 
            v-model="styleData.alt" 
            placeholder="图片描述"
            @change="updateStyle"
          />
        </el-form-item>
        <el-form-item label="适应方式">
          <el-select 
            v-model="styleData.objectFit" 
            @change="updateStyle"
            style="width: 100%"
          >
            <el-option label="包含" value="contain" />
            <el-option label="覆盖" value="cover" />
            <el-option label="填充" value="fill" />
            <el-option label="原始尺寸" value="none" />
            <el-option label="缩小适应" value="scale-down" />
          </el-select>
        </el-form-item>
      </el-form>
    </div>
    
    <div class="property-section">
      <h4>边框样式</h4>
      <el-form label-width="80px" size="small">
        <el-form-item label="边框">
          <el-input 
            v-model="styleData.border" 
            placeholder="如: 1px solid #00f5ff"
            @change="updateStyle"
          />
        </el-form-item>
        <el-form-item label="圆角">
          <el-input-number 
            v-model="styleData.borderRadius" 
            :min="0" 
            :max="100"
            :step="1"
            @change="updateStyle"
            style="width: 100%"
          />
        </el-form-item>
      </el-form>
    </div>
    
    <div class="property-section">
      <h4>背景设置</h4>
      <el-form label-width="80px" size="small">
        <el-form-item label="背景颜色">
          <el-color-picker 
            v-model="styleData.backgroundColor" 
            @change="updateStyle"
            show-alpha
          />
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import { reactive, watch } from 'vue'

const props = defineProps({
  component: { type: Object, required: true }
})

const emit = defineEmits(['update'])

// 响应式数据
const styleData = reactive({
  src: '',
  alt: '',
  objectFit: 'contain',
  border: '',
  borderRadius: 0,
  backgroundColor: 'transparent'
})

// 监听组件变化
watch(() => props.component, (newComponent) => {
  if (newComponent) {
    Object.assign(styleData, {
      src: newComponent.src || '',
      alt: newComponent.alt || '',
      objectFit: newComponent.objectFit || 'contain',
      border: newComponent.border || '',
      borderRadius: newComponent.borderRadius || 0,
      backgroundColor: newComponent.backgroundColor || 'transparent'
    })
  }
}, { immediate: true })

// 方法
const updateStyle = () => {
  emit('update', { ...styleData })
}
</script>

<style scoped>
.image-style-panel {
  width: 100%;
}

.property-section {
  margin-bottom: 1.5rem;
}

.property-section h4 {
  margin: 0 0 1rem 0;
  font-size: 0.9rem;
  color: #00f5ff;
  border-bottom: 1px solid #2a3f5f;
  padding-bottom: 0.5rem;
}
</style>
