<template>
  <div class="default-component" :style="componentStyle">
    <div class="component-icon">
      <el-icon size="30"><Box /></el-icon>
    </div>
    <div class="component-name">{{ config.name }}</div>
    <div class="component-type">{{ config.type }}</div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  config: { type: Object, required: true },
  data: { type: Object, default: () => ({}) }
})

const componentStyle = computed(() => ({
  width: '100%',
  height: '100%',
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  justifyContent: 'center',
  background: 'rgba(255, 255, 255, 0.05)',
  border: '1px dashed rgba(255, 255, 255, 0.3)',
  borderRadius: '4px',
  color: 'rgba(255, 255, 255, 0.7)',
  textAlign: 'center',
  padding: '10px'
}))
</script>

<style scoped>
.default-component {
  user-select: none;
}

.component-icon {
  margin-bottom: 8px;
  opacity: 0.6;
}

.component-name {
  font-size: 12px;
  font-weight: 500;
  margin-bottom: 4px;
}

.component-type {
  font-size: 10px;
  opacity: 0.7;
}
</style>
