<template>
  <div class="text-style-panel">
    <div class="property-section">
      <h4>文字样式</h4>
      <el-form label-width="80px" size="small">
        <el-form-item label="文字内容">
          <el-input 
            v-model="styleData.text" 
            type="textarea"
            :rows="3"
            placeholder="请输入文字内容"
            @change="updateStyle"
          />
        </el-form-item>
        <el-form-item label="字体大小">
          <el-input-number 
            v-model="styleData.fontSize" 
            :min="8" 
            :max="200"
            :step="1"
            @change="updateStyle"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="字体颜色">
          <el-color-picker 
            v-model="styleData.color" 
            @change="updateStyle"
            show-alpha
          />
        </el-form-item>
        <el-form-item label="字体粗细">
          <el-select 
            v-model="styleData.fontWeight" 
            @change="updateStyle"
            style="width: 100%"
          >
            <el-option label="正常" value="normal" />
            <el-option label="粗体" value="bold" />
            <el-option label="100" value="100" />
            <el-option label="200" value="200" />
            <el-option label="300" value="300" />
            <el-option label="400" value="400" />
            <el-option label="500" value="500" />
            <el-option label="600" value="600" />
            <el-option label="700" value="700" />
            <el-option label="800" value="800" />
            <el-option label="900" value="900" />
          </el-select>
        </el-form-item>
        <el-form-item label="字体族">
          <el-select 
            v-model="styleData.fontFamily" 
            @change="updateStyle"
            style="width: 100%"
          >
            <el-option label="微软雅黑" value="Microsoft YaHei" />
            <el-option label="宋体" value="SimSun" />
            <el-option label="黑体" value="SimHei" />
            <el-option label="楷体" value="KaiTi" />
            <el-option label="Arial" value="Arial" />
            <el-option label="Times New Roman" value="Times New Roman" />
            <el-option label="Helvetica" value="Helvetica" />
          </el-select>
        </el-form-item>
        <el-form-item label="行高">
          <el-input-number 
            v-model="styleData.lineHeight" 
            :min="0.5" 
            :max="5"
            :step="0.1"
            @change="updateStyle"
            style="width: 100%"
          />
        </el-form-item>
      </el-form>
    </div>
    
    <div class="property-section">
      <h4>对齐方式</h4>
      <el-form label-width="80px" size="small">
        <el-form-item label="水平对齐">
          <el-radio-group v-model="styleData.textAlign" @change="updateStyle">
            <el-radio-button label="left">左对齐</el-radio-button>
            <el-radio-button label="center">居中</el-radio-button>
            <el-radio-button label="right">右对齐</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="垂直对齐">
          <el-radio-group v-model="styleData.verticalAlign" @change="updateStyle">
            <el-radio-button label="flex-start">顶部</el-radio-button>
            <el-radio-button label="center">居中</el-radio-button>
            <el-radio-button label="flex-end">底部</el-radio-button>
          </el-radio-group>
        </el-form-item>
      </el-form>
    </div>
    
    <div class="property-section">
      <h4>文字效果</h4>
      <el-form label-width="80px" size="small">
        <el-form-item label="文字阴影">
          <el-input 
            v-model="styleData.textShadow" 
            placeholder="如: 2px 2px 4px rgba(0,0,0,0.5)"
            @change="updateStyle"
          />
        </el-form-item>
      </el-form>
    </div>
    
    <div class="property-section">
      <h4>背景样式</h4>
      <el-form label-width="80px" size="small">
        <el-form-item label="背景颜色">
          <el-color-picker 
            v-model="styleData.backgroundColor" 
            @change="updateStyle"
            show-alpha
          />
        </el-form-item>
        <el-form-item label="边框">
          <el-input 
            v-model="styleData.border" 
            placeholder="如: 1px solid #00f5ff"
            @change="updateStyle"
          />
        </el-form-item>
        <el-form-item label="圆角">
          <el-input-number 
            v-model="styleData.borderRadius" 
            :min="0" 
            :max="100"
            :step="1"
            @change="updateStyle"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="内边距">
          <el-input 
            v-model="styleData.padding" 
            placeholder="如: 10px 或 10px 20px"
            @change="updateStyle"
          />
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import { reactive, watch } from 'vue'

const props = defineProps({
  component: { type: Object, required: true }
})

const emit = defineEmits(['update'])

// 响应式数据
const styleData = reactive({
  text: '',
  fontSize: 16,
  color: '#ffffff',
  fontWeight: 'normal',
  fontFamily: 'Microsoft YaHei',
  lineHeight: 1.5,
  textAlign: 'center',
  verticalAlign: 'center',
  textShadow: '',
  backgroundColor: 'transparent',
  border: '',
  borderRadius: 0,
  padding: '0px'
})

// 监听组件变化
watch(() => props.component, (newComponent) => {
  if (newComponent) {
    Object.assign(styleData, {
      text: newComponent.text || '',
      fontSize: newComponent.fontSize || 16,
      color: newComponent.color || '#ffffff',
      fontWeight: newComponent.fontWeight || 'normal',
      fontFamily: newComponent.fontFamily || 'Microsoft YaHei',
      lineHeight: newComponent.lineHeight || 1.5,
      textAlign: newComponent.textAlign || 'center',
      verticalAlign: newComponent.verticalAlign || 'center',
      textShadow: newComponent.textShadow || '',
      backgroundColor: newComponent.backgroundColor || 'transparent',
      border: newComponent.border || '',
      borderRadius: newComponent.borderRadius || 0,
      padding: newComponent.padding || '0px'
    })
  }
}, { immediate: true })

// 方法
const updateStyle = () => {
  emit('update', { ...styleData })
}
</script>

<style scoped>
.text-style-panel {
  width: 100%;
}

.property-section {
  margin-bottom: 1.5rem;
}

.property-section h4 {
  margin: 0 0 1rem 0;
  font-size: 0.9rem;
  color: #00f5ff;
  border-bottom: 1px solid #2a3f5f;
  padding-bottom: 0.5rem;
}

:deep(.el-radio-group) {
  width: 100%;
}

:deep(.el-radio-button) {
  flex: 1;
}

:deep(.el-radio-button__inner) {
  width: 100%;
  font-size: 12px;
}
</style>
