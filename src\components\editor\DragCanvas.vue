<template>
  <div class="drag-canvas-container">
    <!-- 画布工具栏 -->
    <div class="canvas-toolbar">
      <div class="toolbar-left">
        <el-button-group size="small">
          <el-button :type="tool === 'select' ? 'primary' : ''" @click="setTool('select')">
            <el-icon><Mouse /></el-icon>
            选择
          </el-button>
          <el-button :type="tool === 'hand' ? 'primary' : ''" @click="setTool('hand')">
            <el-icon><Rank /></el-icon>
            拖拽
          </el-button>
        </el-button-group>
      </div>
      <div class="toolbar-center">
        <el-button-group size="small">
          <el-button @click="zoomOut" :disabled="zoom <= 10">
            <el-icon><ZoomOut /></el-icon>
          </el-button>
          <span class="zoom-display">{{ zoom }}%</span>
          <el-button @click="zoomIn" :disabled="zoom >= 500">
            <el-icon><ZoomIn /></el-icon>
          </el-button>
          <el-button @click="resetZoom">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-button-group>
      </div>
      <div class="toolbar-right">
        <el-button-group size="small">
          <el-button @click="toggleGrid" :type="showGrid ? 'primary' : ''">
            <el-icon><Grid /></el-icon>
            网格
          </el-button>
          <el-button @click="toggleRuler" :type="showRuler ? 'primary' : ''">
            <el-icon><Operation /></el-icon>
            标尺
          </el-button>
        </el-button-group>
      </div>
    </div>

    <!-- 画布区域 -->
    <div class="canvas-wrapper" ref="canvasWrapper">
      <!-- 标尺 -->
      <div v-if="showRuler" class="rulers">
        <div class="ruler ruler-horizontal">
          <div
            v-for="mark in horizontalMarks"
            :key="mark.value"
            class="ruler-mark"
            :style="{ left: mark.position + 'px' }"
          >
            <span class="ruler-text">{{ mark.value }}</span>
          </div>
        </div>
        <div class="ruler ruler-vertical">
          <div
            v-for="mark in verticalMarks"
            :key="mark.value"
            class="ruler-mark"
            :style="{ top: mark.position + 'px' }"
          >
            <span class="ruler-text">{{ mark.value }}</span>
          </div>
        </div>
      </div>

      <!-- 画布容器 -->
      <div 
        class="canvas-container"
        :style="containerStyle"
        @wheel="handleWheel"
        @mousedown="handleCanvasMouseDown"
        @mousemove="handleCanvasMouseMove"
        @mouseup="handleCanvasMouseUp"
        @mouseleave="handleCanvasMouseLeave"
      >
        <!-- 画布 -->
        <div
          class="canvas"
          :style="canvasStyle"
          @drop="handleDrop"
          @dragover="handleDragOver"
          @click="handleCanvasClick"
        >
          <!-- 网格 -->
          <div v-if="showGrid" class="canvas-grid" :style="gridStyle"></div>
          
          <!-- 辅助线 -->
          <div v-if="guidelines.vertical.length > 0" class="guidelines">
            <div
              v-for="(line, index) in guidelines.vertical"
              :key="'v-' + index"
              class="guideline guideline-vertical"
              :style="{ left: line + 'px' }"
            ></div>
          </div>
          <div v-if="guidelines.horizontal.length > 0" class="guidelines">
            <div
              v-for="(line, index) in guidelines.horizontal"
              :key="'h-' + index"
              class="guideline guideline-horizontal"
              :style="{ top: line + 'px' }"
            ></div>
          </div>

          <!-- 组件渲染 -->
          <DraggableComponent
            v-for="component in components"
            :key="component.id"
            :component="component"
            :selected="selectedComponent?.id === component.id"
            :zoom="zoom"
            @select="selectComponent"
            @update="updateComponent"
            @delete="deleteComponent"
          />

          <!-- 选择框 -->
          <div
            v-if="selectionBox.visible"
            class="selection-box"
            :style="selectionBoxStyle"
          ></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { useStore } from '@/store'
import DraggableComponent from './DraggableComponent.vue'

const props = defineProps({
  width: { type: Number, default: 1920 },
  height: { type: Number, default: 1080 }
})

const emit = defineEmits(['component-select', 'component-update', 'component-delete'])

const { state, actions } = useStore()

// 响应式数据
const canvasWrapper = ref(null)
const tool = ref('select') // select, hand
const zoom = ref(100)
const showGrid = ref(true)
const showRuler = ref(true)
const panOffset = reactive({ x: 0, y: 0 })
const isPanning = ref(false)
const lastPanPoint = reactive({ x: 0, y: 0 })

// 辅助线
const guidelines = reactive({
  vertical: [],
  horizontal: []
})

// 选择框
const selectionBox = reactive({
  visible: false,
  startX: 0,
  startY: 0,
  endX: 0,
  endY: 0
})

// 计算属性
const containerStyle = computed(() => ({
  cursor: tool.value === 'hand' ? 'grab' : 'default'
}))

const canvasStyle = computed(() => ({
  width: props.width + 'px',
  height: props.height + 'px',
  transform: `scale(${zoom.value / 100}) translate(${panOffset.x}px, ${panOffset.y}px)`,
  transformOrigin: '0 0'
}))

const gridStyle = computed(() => {
  const gridSize = 20 * (zoom.value / 100)
  return {
    backgroundSize: `${gridSize}px ${gridSize}px`,
    backgroundImage: `
      linear-gradient(to right, rgba(255, 255, 255, 0.1) 1px, transparent 1px),
      linear-gradient(to bottom, rgba(255, 255, 255, 0.1) 1px, transparent 1px)
    `
  }
})

const selectionBoxStyle = computed(() => ({
  left: Math.min(selectionBox.startX, selectionBox.endX) + 'px',
  top: Math.min(selectionBox.startY, selectionBox.endY) + 'px',
  width: Math.abs(selectionBox.endX - selectionBox.startX) + 'px',
  height: Math.abs(selectionBox.endY - selectionBox.startY) + 'px'
}))

const horizontalMarks = computed(() => {
  const marks = []
  const step = 100
  const canvasWidth = props.width * (zoom.value / 100)
  for (let i = 0; i <= canvasWidth; i += step) {
    marks.push({
      value: Math.round(i / (zoom.value / 100)),
      position: i
    })
  }
  return marks
})

const verticalMarks = computed(() => {
  const marks = []
  const step = 100
  const canvasHeight = props.height * (zoom.value / 100)
  for (let i = 0; i <= canvasHeight; i += step) {
    marks.push({
      value: Math.round(i / (zoom.value / 100)),
      position: i
    })
  }
  return marks
})

const components = computed(() => state.currentProject.components)
const selectedComponent = computed(() => state.editor.selectedComponent)

// 方法
const setTool = (newTool) => {
  tool.value = newTool
}

const zoomIn = () => {
  zoom.value = Math.min(500, zoom.value + 10)
  actions.setZoom(zoom.value)
}

const zoomOut = () => {
  zoom.value = Math.max(10, zoom.value - 10)
  actions.setZoom(zoom.value)
}

const resetZoom = () => {
  zoom.value = 100
  panOffset.x = 0
  panOffset.y = 0
  actions.setZoom(zoom.value)
}

const toggleGrid = () => {
  showGrid.value = !showGrid.value
  actions.toggleGrid()
}

const toggleRuler = () => {
  showRuler.value = !showRuler.value
  actions.toggleRuler()
}

const handleWheel = (event) => {
  event.preventDefault()
  
  if (event.ctrlKey) {
    // 缩放
    const delta = event.deltaY > 0 ? -10 : 10
    const newZoom = Math.max(10, Math.min(500, zoom.value + delta))
    zoom.value = newZoom
    actions.setZoom(newZoom)
  } else {
    // 平移
    panOffset.x -= event.deltaX
    panOffset.y -= event.deltaY
  }
}

const handleCanvasMouseDown = (event) => {
  if (tool.value === 'hand') {
    isPanning.value = true
    lastPanPoint.x = event.clientX
    lastPanPoint.y = event.clientY
    event.target.style.cursor = 'grabbing'
  } else if (tool.value === 'select' && event.target.classList.contains('canvas')) {
    // 开始选择框
    const rect = event.target.getBoundingClientRect()
    selectionBox.startX = (event.clientX - rect.left) / (zoom.value / 100)
    selectionBox.startY = (event.clientY - rect.top) / (zoom.value / 100)
    selectionBox.endX = selectionBox.startX
    selectionBox.endY = selectionBox.startY
    selectionBox.visible = true
  }
}

const handleCanvasMouseMove = (event) => {
  if (isPanning.value) {
    const deltaX = event.clientX - lastPanPoint.x
    const deltaY = event.clientY - lastPanPoint.y
    panOffset.x += deltaX
    panOffset.y += deltaY
    lastPanPoint.x = event.clientX
    lastPanPoint.y = event.clientY
  } else if (selectionBox.visible) {
    const rect = event.currentTarget.querySelector('.canvas').getBoundingClientRect()
    selectionBox.endX = (event.clientX - rect.left) / (zoom.value / 100)
    selectionBox.endY = (event.clientY - rect.top) / (zoom.value / 100)
  }
}

const handleCanvasMouseUp = (event) => {
  if (isPanning.value) {
    isPanning.value = false
    event.target.style.cursor = tool.value === 'hand' ? 'grab' : 'default'
  } else if (selectionBox.visible) {
    // 结束选择框，选择框内的组件
    selectComponentsInBox()
    selectionBox.visible = false
  }
}

const handleCanvasMouseLeave = () => {
  isPanning.value = false
  selectionBox.visible = false
}

const handleCanvasClick = (event) => {
  if (event.target.classList.contains('canvas')) {
    // 点击空白区域，取消选择
    selectComponent(null)
  }
}

const handleDrop = (event) => {
  event.preventDefault()
  const componentData = JSON.parse(event.dataTransfer.getData('component'))
  
  // 计算放置位置
  const rect = event.currentTarget.getBoundingClientRect()
  const x = (event.clientX - rect.left) / (zoom.value / 100) - panOffset.x
  const y = (event.clientY - rect.top) / (zoom.value / 100) - panOffset.y
  
  // 创建新组件
  const newComponent = {
    id: Date.now(),
    ...componentData.defaultConfig,
    name: componentData.name,
    type: componentData.type,
    x: Math.max(0, x - (componentData.defaultConfig.width / 2)),
    y: Math.max(0, y - (componentData.defaultConfig.height / 2)),
    zIndex: components.value.length,
    locked: false,
    visible: true
  }
  
  actions.addComponent(newComponent)
  selectComponent(newComponent)
}

const handleDragOver = (event) => {
  event.preventDefault()
}

const selectComponent = (component) => {
  actions.setSelectedComponent(component)
  emit('component-select', component)
}

const updateComponent = (componentId, updates) => {
  actions.updateComponent(componentId, updates)
  emit('component-update', componentId, updates)
}

const deleteComponent = (componentId) => {
  actions.removeComponent(componentId)
  emit('component-delete', componentId)
}

const selectComponentsInBox = () => {
  // 实现选择框内组件的选择逻辑
  const boxLeft = Math.min(selectionBox.startX, selectionBox.endX)
  const boxTop = Math.min(selectionBox.startY, selectionBox.endY)
  const boxRight = Math.max(selectionBox.startX, selectionBox.endX)
  const boxBottom = Math.max(selectionBox.startY, selectionBox.endY)
  
  const selectedComponents = components.value.filter(component => {
    return component.x >= boxLeft &&
           component.y >= boxTop &&
           component.x + component.width <= boxRight &&
           component.y + component.height <= boxBottom
  })
  
  if (selectedComponents.length === 1) {
    selectComponent(selectedComponents[0])
  } else if (selectedComponents.length > 1) {
    // 多选逻辑，暂时选择第一个
    selectComponent(selectedComponents[0])
  }
}

// 键盘事件处理
const handleKeyDown = (event) => {
  if (event.key === 'Delete' && selectedComponent.value) {
    deleteComponent(selectedComponent.value.id)
  } else if (event.ctrlKey && event.key === 'z') {
    actions.undo()
  } else if (event.ctrlKey && event.key === 'y') {
    actions.redo()
  } else if (event.ctrlKey && event.key === 'c' && selectedComponent.value) {
    actions.copyComponent(selectedComponent.value)
  } else if (event.ctrlKey && event.key === 'v') {
    const newComponent = actions.pasteComponent()
    if (newComponent) {
      selectComponent(newComponent)
    }
  }
}

// 生命周期
onMounted(() => {
  document.addEventListener('keydown', handleKeyDown)
  
  // 初始化状态
  zoom.value = state.editor.zoom
  showGrid.value = state.editor.showGrid
  showRuler.value = state.editor.showRuler
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeyDown)
})
</script>

<style scoped>
.drag-canvas-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #0f1419;
}

.canvas-toolbar {
  height: 50px;
  background: #16213e;
  border-bottom: 1px solid #2a3f5f;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 1rem;
}

.zoom-display {
  padding: 0 1rem;
  font-size: 0.9rem;
  color: #999;
  min-width: 50px;
  text-align: center;
}

.canvas-wrapper {
  flex: 1;
  position: relative;
  overflow: hidden;
}

.rulers {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 10;
  pointer-events: none;
}

.ruler {
  position: absolute;
  background: rgba(22, 33, 62, 0.9);
  border: 1px solid #2a3f5f;
}

.ruler-horizontal {
  top: 0;
  left: 30px;
  right: 0;
  height: 30px;
}

.ruler-vertical {
  top: 30px;
  left: 0;
  bottom: 0;
  width: 30px;
}

.ruler-mark {
  position: absolute;
  color: #999;
  font-size: 10px;
}

.ruler-horizontal .ruler-mark {
  top: 5px;
}

.ruler-vertical .ruler-mark {
  left: 5px;
  writing-mode: vertical-lr;
}

.canvas-container {
  width: 100%;
  height: 100%;
  overflow: auto;
  position: relative;
  padding: 50px;
}

.canvas {
  background: #1a1a2e;
  border: 2px solid #2a3f5f;
  position: relative;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
  margin: auto;
}

.canvas-grid {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.guidelines {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.guideline {
  position: absolute;
  background: #00f5ff;
  z-index: 100;
}

.guideline-vertical {
  width: 1px;
  height: 100%;
}

.guideline-horizontal {
  width: 100%;
  height: 1px;
}

.selection-box {
  position: absolute;
  border: 1px dashed #00f5ff;
  background: rgba(0, 245, 255, 0.1);
  pointer-events: none;
  z-index: 200;
}
</style>
