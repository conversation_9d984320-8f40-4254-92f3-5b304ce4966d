/**
 * 数据源相关API
 */

// 模拟数据源数据
const mockDataSources = [
  {
    id: 1,
    name: '销售数据API',
    type: 'api',
    url: 'https://api.example.com/sales',
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer token123'
    },
    params: {},
    updateInterval: 30000, // 30秒更新一次
    status: 'active',
    createTime: '2024-01-15 10:30:00'
  },
  {
    id: 2,
    name: '用户统计数据',
    type: 'static',
    data: [
      { date: '2024-01', users: 1200, newUsers: 300 },
      { date: '2024-02', users: 1350, newUsers: 280 },
      { date: '2024-03', users: 1500, newUsers: 320 },
      { date: '2024-04', users: 1680, newUsers: 350 },
      { date: '2024-05', users: 1820, newUsers: 380 },
      { date: '2024-06', users: 2000, newUsers: 400 }
    ],
    status: 'active',
    createTime: '2024-01-16 14:20:00'
  },
  {
    id: 3,
    name: 'MySQL数据库',
    type: 'mysql',
    description: 'MySQL分析数据库',
    host: '*************',
    port: 3306,
    database: 'analytics',
    username: 'readonly',
    password: '******',
    query: 'SELECT * FROM daily_stats WHERE date >= CURDATE() - INTERVAL 30 DAY',
    updateInterval: 60000, // 1分钟更新一次
    timeout: 30,
    status: 'active',
    createTime: '2024-01-18 09:15:00',
    updateTime: '2024-01-22 16:30:00'
  },
  {
    id: 4,
    name: '产品销量统计',
    type: 'api',
    description: '实时产品销量统计API',
    url: 'https://jsonplaceholder.typicode.com/posts',
    method: 'GET',
    headers: { 'Content-Type': 'application/json' },
    params: {},
    updateInterval: 120000, // 2分钟更新一次
    timeout: 30,
    status: 'active',
    createTime: '2024-01-16 15:30:00',
    updateTime: '2024-01-21 10:15:00'
  },
  {
    id: 5,
    name: '财务报表数据',
    type: 'static',
    description: '月度财务报表数据',
    staticData: JSON.stringify([
      { month: '1月', revenue: 120000, cost: 80000, profit: 40000 },
      { month: '2月', revenue: 150000, cost: 95000, profit: 55000 },
      { month: '3月', revenue: 180000, cost: 110000, profit: 70000 },
      { month: '4月', revenue: 200000, cost: 125000, profit: 75000 },
      { month: '5月', revenue: 220000, cost: 140000, profit: 80000 },
      { month: '6月', revenue: 250000, cost: 155000, profit: 95000 }
    ]),
    updateInterval: 0,
    timeout: 30,
    status: 'active',
    createTime: '2024-01-18 09:45:00',
    updateTime: '2024-01-22 14:30:00'
  },
  {
    id: 6,
    name: 'PostgreSQL数据仓库',
    type: 'postgresql',
    description: 'PostgreSQL数据仓库',
    host: '*************',
    port: 5432,
    database: 'warehouse',
    username: 'analyst',
    password: '******',
    query: 'SELECT product_id, product_name, sales_amount FROM product_sales ORDER BY sales_amount DESC LIMIT 50',
    updateInterval: 300000, // 5分钟更新一次
    timeout: 60,
    status: 'error',
    createTime: '2024-01-19 11:20:00',
    updateTime: '2024-01-23 09:45:00'
  },
  {
    id: 7,
    name: '用户行为数据',
    type: 'api',
    description: '用户行为分析API',
    url: 'https://api.analytics.com/user-behavior',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9'
    },
    body: JSON.stringify({ timeRange: '7d', metrics: ['pageviews', 'sessions', 'bounceRate'] }),
    updateInterval: 180000, // 3分钟更新一次
    timeout: 45,
    status: 'untested',
    createTime: '2024-01-20 14:15:00',
    updateTime: '2024-01-20 14:15:00'
  }
]

/**
 * 获取数据源列表
 * @param {Object} params - 查询参数
 */
export function getDataSourceList(params = {}) {
  const { page = 1, pageSize = 10, type = '', status = '' } = params
  
  return new Promise(resolve => {
    setTimeout(() => {
      let filteredSources = [...mockDataSources]
      
      // 类型筛选
      if (type) {
        filteredSources = filteredSources.filter(source => source.type === type)
      }
      
      // 状态筛选
      if (status) {
        filteredSources = filteredSources.filter(source => source.status === status)
      }
      
      // 分页
      const start = (page - 1) * pageSize
      const end = start + pageSize
      const list = filteredSources.slice(start, end)
      
      resolve({
        code: 200,
        msg: '获取成功',
        data: {
          list,
          total: filteredSources.length,
          page,
          pageSize
        }
      })
    }, 200)
  })
}

/**
 * 获取数据源详情
 * @param {number} id - 数据源ID
 */
export function getDataSourceDetail(id) {
  return new Promise(resolve => {
    setTimeout(() => {
      const dataSource = mockDataSources.find(ds => ds.id == id)
      if (dataSource) {
        resolve({
          code: 200,
          msg: '获取成功',
          data: dataSource
        })
      } else {
        resolve({
          code: 404,
          msg: '数据源不存在',
          data: null
        })
      }
    }, 150)
  })
}

/**
 * 创建数据源
 * @param {Object} dataSourceData - 数据源数据
 */
export function createDataSource(dataSourceData) {
  return new Promise(resolve => {
    setTimeout(() => {
      const newDataSource = {
        id: Date.now(),
        ...dataSourceData,
        status: 'active',
        createTime: new Date().toLocaleString()
      }
      
      mockDataSources.unshift(newDataSource)
      
      resolve({
        code: 200,
        msg: '创建成功',
        data: newDataSource
      })
    }, 500)
  })
}

/**
 * 更新数据源
 * @param {number} id - 数据源ID
 * @param {Object} dataSourceData - 数据源数据
 */
export function updateDataSource(id, dataSourceData) {
  return new Promise(resolve => {
    setTimeout(() => {
      const index = mockDataSources.findIndex(ds => ds.id == id)
      if (index !== -1) {
        mockDataSources[index] = {
          ...mockDataSources[index],
          ...dataSourceData
        }
        
        resolve({
          code: 200,
          msg: '更新成功',
          data: mockDataSources[index]
        })
      } else {
        resolve({
          code: 404,
          msg: '数据源不存在',
          data: null
        })
      }
    }, 400)
  })
}

/**
 * 删除数据源
 * @param {number} id - 数据源ID
 */
export function deleteDataSource(id) {
  return new Promise(resolve => {
    setTimeout(() => {
      const index = mockDataSources.findIndex(ds => ds.id == id)
      if (index !== -1) {
        mockDataSources.splice(index, 1)
        resolve({
          code: 200,
          msg: '删除成功',
          data: null
        })
      } else {
        resolve({
          code: 404,
          msg: '数据源不存在',
          data: null
        })
      }
    }, 300)
  })
}

/**
 * 测试数据源连接
 * @param {Object} dataSourceConfig - 数据源配置
 */
export function testDataSource(dataSourceConfig) {
  return new Promise(resolve => {
    setTimeout(() => {
      // 模拟测试结果
      const success = Math.random() > 0.2 // 80%成功率
      
      if (success) {
        resolve({
          code: 200,
          msg: '连接测试成功',
          data: {
            status: 'success',
            responseTime: Math.floor(Math.random() * 500) + 100,
            sampleData: [
              { id: 1, name: '示例数据1', value: 100 },
              { id: 2, name: '示例数据2', value: 200 },
              { id: 3, name: '示例数据3', value: 150 }
            ]
          }
        })
      } else {
        resolve({
          code: 500,
          msg: '连接测试失败',
          data: {
            status: 'error',
            error: '连接超时或认证失败'
          }
        })
      }
    }, 1000)
  })
}

/**
 * 获取数据源数据
 * @param {number} id - 数据源ID
 * @param {Object} params - 查询参数
 */
export function getDataSourceData(id, params = {}) {
  return new Promise(resolve => {
    setTimeout(() => {
      const dataSource = mockDataSources.find(ds => ds.id == id)
      if (!dataSource) {
        resolve({
          code: 404,
          msg: '数据源不存在',
          data: null
        })
        return
      }
      
      let data = []
      
      // 根据数据源类型返回不同的模拟数据
      switch (dataSource.type) {
        case 'static':
          try {
            data = dataSource.staticData ? JSON.parse(dataSource.staticData) : (dataSource.data || [])
          } catch (error) {
            data = dataSource.data || []
          }
          break

        case 'api':
          // 根据不同的API返回不同的模拟数据
          if (dataSource.url.includes('sales')) {
            data = [
              { date: '2024-01', sales: 12000, orders: 150, growth: 5.2 },
              { date: '2024-02', sales: 15000, orders: 180, growth: 8.1 },
              { date: '2024-03', sales: 18000, orders: 220, growth: 12.3 },
              { date: '2024-04', sales: 16000, orders: 200, growth: -2.1 },
              { date: '2024-05', sales: 20000, orders: 250, growth: 15.8 },
              { date: '2024-06', sales: 22000, orders: 280, growth: 18.9 }
            ]
          } else if (dataSource.url.includes('jsonplaceholder')) {
            // 模拟真实API数据
            data = [
              { id: 1, title: '产品A', userId: 1, views: 1250, sales: 89 },
              { id: 2, title: '产品B', userId: 2, views: 980, sales: 67 },
              { id: 3, title: '产品C', userId: 3, views: 1580, sales: 123 },
              { id: 4, title: '产品D', userId: 4, views: 750, sales: 45 },
              { id: 5, title: '产品E', userId: 5, views: 2100, sales: 156 }
            ]
          } else if (dataSource.url.includes('user-behavior')) {
            data = [
              { date: '2024-01-15', pageviews: 15420, sessions: 3240, bounceRate: 0.32 },
              { date: '2024-01-16', pageviews: 18650, sessions: 3890, bounceRate: 0.28 },
              { date: '2024-01-17', pageviews: 16780, sessions: 3560, bounceRate: 0.35 },
              { date: '2024-01-18', pageviews: 19230, sessions: 4120, bounceRate: 0.29 },
              { date: '2024-01-19', pageviews: 21450, sessions: 4580, bounceRate: 0.26 },
              { date: '2024-01-20', pageviews: 23100, sessions: 4920, bounceRate: 0.24 },
              { date: '2024-01-21', pageviews: 20890, sessions: 4450, bounceRate: 0.31 }
            ]
          } else {
            // 默认API数据
            data = [
              { id: 1, name: '数据项1', value: Math.floor(Math.random() * 1000) },
              { id: 2, name: '数据项2', value: Math.floor(Math.random() * 1000) },
              { id: 3, name: '数据项3', value: Math.floor(Math.random() * 1000) },
              { id: 4, name: '数据项4', value: Math.floor(Math.random() * 1000) },
              { id: 5, name: '数据项5', value: Math.floor(Math.random() * 1000) }
            ]
          }
          break

        case 'mysql':
        case 'postgresql':
        case 'mongodb':
          // 模拟数据库查询结果
          if (dataSource.query.toLowerCase().includes('product')) {
            data = [
              { product_id: 1, product_name: '智能手机', sales_amount: 125000, quantity: 250 },
              { product_id: 2, product_name: '笔记本电脑', sales_amount: 89000, quantity: 89 },
              { product_id: 3, product_name: '平板电脑', sales_amount: 67000, quantity: 134 },
              { product_id: 4, product_name: '智能手表', sales_amount: 45000, quantity: 180 },
              { product_id: 5, product_name: '无线耳机', sales_amount: 34000, quantity: 340 }
            ]
          } else if (dataSource.query.toLowerCase().includes('user')) {
            data = [
              { user_id: 1, username: 'user001', login_count: 45, last_login: '2024-01-22 10:30:00' },
              { user_id: 2, username: 'user002', login_count: 32, last_login: '2024-01-22 09:15:00' },
              { user_id: 3, username: 'user003', login_count: 67, last_login: '2024-01-22 11:45:00' },
              { user_id: 4, username: 'user004', login_count: 23, last_login: '2024-01-21 16:20:00' },
              { user_id: 5, username: 'user005', login_count: 89, last_login: '2024-01-22 08:30:00' }
            ]
          } else {
            data = [
              { id: 1, category: '电子产品', amount: 5000, count: 50, percentage: 35.2 },
              { id: 2, category: '服装', amount: 3000, count: 80, percentage: 21.1 },
              { id: 3, category: '食品', amount: 2000, count: 120, percentage: 14.1 },
              { id: 4, category: '图书', amount: 1500, count: 200, percentage: 10.6 },
              { id: 5, category: '家居', amount: 4000, count: 60, percentage: 28.2 }
            ]
          }
          break

        case 'csv':
        case 'json':
          // 模拟文件数据
          data = [
            { region: '华北', sales: 125000, target: 120000, completion: 104.2 },
            { region: '华东', sales: 189000, target: 180000, completion: 105.0 },
            { region: '华南', sales: 156000, target: 150000, completion: 104.0 },
            { region: '华中', sales: 98000, target: 100000, completion: 98.0 },
            { region: '西北', sales: 67000, target: 70000, completion: 95.7 },
            { region: '西南', sales: 89000, target: 85000, completion: 104.7 }
          ]
          break

        default:
          data = []
      }
      
      resolve({
        code: 200,
        msg: '获取成功',
        data: {
          source: dataSource,
          data,
          updateTime: new Date().toLocaleString()
        }
      })
    }, 300)
  })
}
