<template>
  <div class="property-panel">
    <div class="panel-header">
      <h3>属性设置</h3>
    </div>
    
    <div class="panel-content" v-if="selectedComponent">
      <el-tabs v-model="activeTab" type="border-card">
        <!-- 基础属性 -->
        <el-tab-pane label="基础" name="basic">
          <div class="property-section">
            <h4>基本信息</h4>
            <el-form label-width="80px" size="small">
              <el-form-item label="组件名称">
                <el-input 
                  v-model="componentData.name" 
                  @change="updateComponent"
                  placeholder="请输入组件名称"
                />
              </el-form-item>
              <el-form-item label="组件类型">
                <el-input v-model="componentData.type" disabled />
              </el-form-item>
            </el-form>
          </div>
          
          <div class="property-section">
            <h4>位置和尺寸</h4>
            <el-form label-width="80px" size="small">
              <el-form-item label="X坐标">
                <el-input-number 
                  v-model="componentData.x" 
                  :min="0" 
                  :step="1"
                  @change="updateComponent"
                  style="width: 100%"
                />
              </el-form-item>
              <el-form-item label="Y坐标">
                <el-input-number 
                  v-model="componentData.y" 
                  :min="0" 
                  :step="1"
                  @change="updateComponent"
                  style="width: 100%"
                />
              </el-form-item>
              <el-form-item label="宽度">
                <el-input-number 
                  v-model="componentData.width" 
                  :min="1" 
                  :step="1"
                  @change="updateComponent"
                  style="width: 100%"
                />
              </el-form-item>
              <el-form-item label="高度">
                <el-input-number 
                  v-model="componentData.height" 
                  :min="1" 
                  :step="1"
                  @change="updateComponent"
                  style="width: 100%"
                />
              </el-form-item>
              <el-form-item label="旋转角度">
                <el-input-number 
                  v-model="componentData.rotation" 
                  :min="-360" 
                  :max="360"
                  :step="1"
                  @change="updateComponent"
                  style="width: 100%"
                />
              </el-form-item>
              <el-form-item label="层级">
                <el-input-number 
                  v-model="componentData.zIndex" 
                  :min="0" 
                  :step="1"
                  @change="updateComponent"
                  style="width: 100%"
                />
              </el-form-item>
            </el-form>
          </div>
          
          <div class="property-section">
            <h4>状态控制</h4>
            <el-form label-width="80px" size="small">
              <el-form-item label="锁定">
                <el-switch 
                  v-model="componentData.locked" 
                  @change="updateComponent"
                />
              </el-form-item>
              <el-form-item label="显示">
                <el-switch 
                  v-model="componentData.visible" 
                  @change="updateComponent"
                />
              </el-form-item>
            </el-form>
          </div>
        </el-tab-pane>
        
        <!-- 样式属性 -->
        <el-tab-pane label="样式" name="style">
          <component 
            :is="getStyleComponent(selectedComponent.type)"
            :component="selectedComponent"
            @update="handleStyleUpdate"
          />
        </el-tab-pane>
        
        <!-- 数据属性 -->
        <el-tab-pane label="数据" name="data">
          <component 
            :is="getDataComponent(selectedComponent.type)"
            :component="selectedComponent"
            @update="handleDataUpdate"
          />
        </el-tab-pane>
        
        <!-- 交互属性 -->
        <el-tab-pane label="交互" name="interaction">
          <div class="property-section">
            <h4>事件配置</h4>
            <el-form label-width="80px" size="small">
              <el-form-item label="点击事件">
                <el-select 
                  v-model="componentData.onClick" 
                  placeholder="选择点击事件"
                  style="width: 100%"
                  @change="updateComponent"
                >
                  <el-option label="无" value="" />
                  <el-option label="跳转链接" value="link" />
                  <el-option label="弹出窗口" value="modal" />
                  <el-option label="刷新数据" value="refresh" />
                </el-select>
              </el-form-item>
              <el-form-item label="悬停效果" v-if="componentData.onClick === 'link'">
                <el-input 
                  v-model="componentData.linkUrl" 
                  placeholder="请输入链接地址"
                  @change="updateComponent"
                />
              </el-form-item>
            </el-form>
          </div>
          
          <div class="property-section">
            <h4>动画效果</h4>
            <el-form label-width="80px" size="small">
              <el-form-item label="入场动画">
                <el-select 
                  v-model="componentData.enterAnimation" 
                  placeholder="选择入场动画"
                  style="width: 100%"
                  @change="updateComponent"
                >
                  <el-option label="无" value="" />
                  <el-option label="淡入" value="fadeIn" />
                  <el-option label="滑入" value="slideIn" />
                  <el-option label="缩放" value="zoomIn" />
                </el-select>
              </el-form-item>
              <el-form-item label="动画时长">
                <el-input-number 
                  v-model="componentData.animationDuration" 
                  :min="0" 
                  :max="10"
                  :step="0.1"
                  @change="updateComponent"
                  style="width: 100%"
                />
                <span style="margin-left: 8px; color: #999; font-size: 12px;">秒</span>
              </el-form-item>
            </el-form>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
    
    <!-- 无选中组件时的提示 -->
    <div v-else class="no-selection">
      <el-icon size="40"><Setting /></el-icon>
      <p>请选择一个组件来编辑属性</p>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { useStore } from '@/store'
import TextStylePanel from './property-panels/TextStylePanel.vue'
import ChartStylePanel from './property-panels/ChartStylePanel.vue'
import ImageStylePanel from './property-panels/ImageStylePanel.vue'
import GaugeStylePanel from './property-panels/GaugeStylePanel.vue'
import TextDataPanel from './property-panels/TextDataPanel.vue'
import ChartDataPanel from './property-panels/ChartDataPanel.vue'

const { state, actions } = useStore()

// 响应式数据
const activeTab = ref('basic')
const componentData = reactive({})

// 计算属性
const selectedComponent = computed(() => state.editor.selectedComponent)

// 监听选中组件变化
watch(selectedComponent, (newComponent) => {
  if (newComponent) {
    Object.assign(componentData, {
      ...newComponent,
      rotation: newComponent.rotation || 0,
      locked: newComponent.locked || false,
      visible: newComponent.visible !== false,
      onClick: newComponent.onClick || '',
      linkUrl: newComponent.linkUrl || '',
      enterAnimation: newComponent.enterAnimation || '',
      animationDuration: newComponent.animationDuration || 1
    })
  }
}, { immediate: true })

// 方法
const updateComponent = () => {
  if (selectedComponent.value) {
    actions.updateComponent(selectedComponent.value.id, { ...componentData })
  }
}

const getStyleComponent = (type) => {
  const styleComponentMap = {
    'text-title': TextStylePanel,
    'text-content': TextStylePanel,
    'bar-chart': ChartStylePanel,
    'line-chart': ChartStylePanel,
    'pie-chart': ChartStylePanel,
    'scatter-chart': ChartStylePanel,
    'radar-chart': ChartStylePanel,
    'gauge': GaugeStylePanel,
    'progress': 'div', // 进度条样式较简单，暂时使用默认
    'counter': 'div', // 数字翻牌器样式较简单，暂时使用默认
    'table': 'div', // 表格样式较复杂，暂时使用默认
    'image': ImageStylePanel
  }
  return styleComponentMap[type] || 'div'
}

const getDataComponent = (type) => {
  const dataComponentMap = {
    'text-title': TextDataPanel,
    'text-content': TextDataPanel,
    'bar-chart': ChartDataPanel,
    'line-chart': ChartDataPanel,
    'pie-chart': ChartDataPanel,
    'scatter-chart': ChartDataPanel,
    'radar-chart': ChartDataPanel,
    'gauge': 'div', // 仪表盘数据配置较简单
    'progress': 'div', // 进度条数据配置较简单
    'counter': 'div', // 数字翻牌器数据配置较简单
    'table': ChartDataPanel // 表格可以复用图表的数据配置
  }
  return dataComponentMap[type] || 'div'
}

const handleStyleUpdate = (updates) => {
  Object.assign(componentData, updates)
  updateComponent()
}

const handleDataUpdate = (updates) => {
  Object.assign(componentData, updates)
  updateComponent()
}
</script>

<style scoped>
.property-panel {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #16213e;
}

.panel-header {
  height: 50px;
  display: flex;
  align-items: center;
  padding: 0 1rem;
  border-bottom: 1px solid #2a3f5f;
}

.panel-header h3 {
  margin: 0;
  font-size: 1rem;
  color: #ffffff;
}

.panel-content {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
}

.property-section {
  margin-bottom: 1.5rem;
}

.property-section h4 {
  margin: 0 0 1rem 0;
  font-size: 0.9rem;
  color: #00f5ff;
  border-bottom: 1px solid #2a3f5f;
  padding-bottom: 0.5rem;
}

.no-selection {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #999;
  text-align: center;
}

.no-selection p {
  margin-top: 1rem;
  font-size: 0.9rem;
}

:deep(.el-tabs__header) {
  margin: 0;
}

:deep(.el-tabs__content) {
  padding: 1rem 0;
}

:deep(.el-form-item) {
  margin-bottom: 1rem;
}

:deep(.el-form-item__label) {
  color: #ffffff;
  font-size: 0.8rem;
}

:deep(.el-input__inner) {
  background: rgba(255, 255, 255, 0.1);
  border-color: #2a3f5f;
  color: #ffffff;
}

:deep(.el-input-number .el-input__inner) {
  background: rgba(255, 255, 255, 0.1);
  border-color: #2a3f5f;
  color: #ffffff;
}

:deep(.el-select .el-input__inner) {
  background: rgba(255, 255, 255, 0.1);
  border-color: #2a3f5f;
  color: #ffffff;
}
</style>
