<template>
  <div class="table-component" :style="containerStyle">
    <!-- 表格标题 -->
    <div v-if="showTitle" class="table-title" :style="titleStyle">
      {{ title }}
    </div>
    
    <!-- 表格内容 -->
    <div class="table-wrapper" :style="wrapperStyle">
      <table class="data-table" :style="tableStyle">
        <!-- 表头 -->
        <thead v-if="showHeader" class="table-header">
          <tr>
            <th
              v-for="column in columns"
              :key="column.key"
              :style="getHeaderCellStyle(column)"
              :class="{ sortable: column.sortable }"
              @click="handleSort(column)"
            >
              <div class="header-content">
                <span>{{ column.title }}</span>
                <el-icon v-if="column.sortable" class="sort-icon">
                  <component :is="getSortIcon(column.key)" />
                </el-icon>
              </div>
            </th>
          </tr>
        </thead>
        
        <!-- 表体 -->
        <tbody class="table-body">
          <tr
            v-for="(row, index) in displayData"
            :key="index"
            :style="getRowStyle(row, index)"
            :class="{ 'row-hover': config.rowHover !== false }"
            @click="handleRowClick(row, index)"
          >
            <td
              v-for="column in columns"
              :key="column.key"
              :style="getCellStyle(column, row, index)"
            >
              <div class="cell-content">
                <!-- 自定义渲染 -->
                <template v-if="column.render">
                  <component
                    :is="column.render"
                    :value="getCellValue(row, column.key)"
                    :row="row"
                    :column="column"
                    :index="index"
                  />
                </template>
                <!-- 默认渲染 -->
                <template v-else>
                  {{ formatCellValue(getCellValue(row, column.key), column) }}
                </template>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
      
      <!-- 空数据提示 -->
      <div v-if="displayData.length === 0" class="empty-data" :style="emptyStyle">
        <el-icon size="40"><Document /></el-icon>
        <p>{{ emptyText }}</p>
      </div>
    </div>
    
    <!-- 分页器 -->
    <div v-if="showPagination && totalPages > 1" class="table-pagination">
      <el-pagination
        v-model:current-page="currentPage"
        :page-size="pageSize"
        :total="totalCount"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'

const props = defineProps({
  config: { type: Object, required: true },
  data: { type: Object, default: () => ({}) }
})

const emit = defineEmits(['row-click', 'sort-change'])

// 响应式数据
const currentPage = ref(1)
const pageSize = ref(10)
const sortField = ref('')
const sortOrder = ref('') // 'asc' | 'desc' | ''

// 计算属性
const tableData = computed(() => {
  return props.data.data || props.config.data || []
})

const columns = computed(() => {
  return props.config.columns || []
})

const title = computed(() => props.config.title || '')
const showTitle = computed(() => props.config.showTitle !== false && title.value)
const showHeader = computed(() => props.config.showHeader !== false)
const showPagination = computed(() => props.config.showPagination === true)
const emptyText = computed(() => props.config.emptyText || '暂无数据')

// 排序和分页
const sortedData = computed(() => {
  if (!sortField.value || !sortOrder.value) {
    return [...tableData.value]
  }
  
  return [...tableData.value].sort((a, b) => {
    const aVal = getCellValue(a, sortField.value)
    const bVal = getCellValue(b, sortField.value)
    
    if (typeof aVal === 'number' && typeof bVal === 'number') {
      return sortOrder.value === 'asc' ? aVal - bVal : bVal - aVal
    } else {
      const aStr = String(aVal).toLowerCase()
      const bStr = String(bVal).toLowerCase()
      if (sortOrder.value === 'asc') {
        return aStr.localeCompare(bStr)
      } else {
        return bStr.localeCompare(aStr)
      }
    }
  })
})

const totalCount = computed(() => sortedData.value.length)
const totalPages = computed(() => Math.ceil(totalCount.value / pageSize.value))

const displayData = computed(() => {
  if (!showPagination.value) {
    return sortedData.value
  }
  
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return sortedData.value.slice(start, end)
})

// 样式计算
const containerStyle = computed(() => ({
  background: props.config.backgroundColor || 'transparent',
  border: props.config.border || '1px solid rgba(255, 255, 255, 0.1)',
  borderRadius: props.config.borderRadius || '8px',
  padding: props.config.padding || '16px',
  height: '100%',
  display: 'flex',
  flexDirection: 'column'
}))

const titleStyle = computed(() => ({
  fontSize: (props.config.titleFontSize || 18) + 'px',
  color: props.config.titleColor || '#ffffff',
  fontWeight: props.config.titleFontWeight || 'bold',
  marginBottom: '16px',
  textAlign: props.config.titleAlign || 'left'
}))

const wrapperStyle = computed(() => ({
  flex: 1,
  overflow: 'auto',
  background: props.config.tableBackground || 'rgba(255, 255, 255, 0.02)'
}))

const tableStyle = computed(() => ({
  width: '100%',
  borderCollapse: 'collapse',
  fontSize: (props.config.fontSize || 14) + 'px',
  color: props.config.color || '#ffffff'
}))

const emptyStyle = computed(() => ({
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  justifyContent: 'center',
  height: '200px',
  color: '#999',
  fontSize: '14px'
}))

// 方法
const getCellValue = (row, key) => {
  return key.split('.').reduce((obj, k) => obj?.[k], row)
}

const formatCellValue = (value, column) => {
  if (value === null || value === undefined) {
    return column.defaultValue || '-'
  }
  
  if (column.format) {
    switch (column.format) {
      case 'number':
        return Number(value).toLocaleString()
      case 'currency':
        return '￥' + Number(value).toLocaleString()
      case 'percent':
        return (Number(value) * 100).toFixed(2) + '%'
      case 'date':
        return new Date(value).toLocaleDateString()
      case 'datetime':
        return new Date(value).toLocaleString()
      default:
        return value
    }
  }
  
  return value
}

const getHeaderCellStyle = (column) => ({
  padding: props.config.cellPadding || '12px 8px',
  background: props.config.headerBackground || 'rgba(0, 245, 255, 0.1)',
  borderBottom: props.config.headerBorder || '2px solid #00f5ff',
  color: props.config.headerColor || '#00f5ff',
  fontWeight: props.config.headerFontWeight || 'bold',
  textAlign: column.align || 'left',
  width: column.width || 'auto',
  minWidth: column.minWidth || 'auto',
  cursor: column.sortable ? 'pointer' : 'default'
})

const getRowStyle = (row, index) => ({
  background: index % 2 === 0 
    ? (props.config.evenRowBackground || 'transparent')
    : (props.config.oddRowBackground || 'rgba(255, 255, 255, 0.02)'),
  transition: 'background-color 0.2s ease'
})

const getCellStyle = (column, row, index) => ({
  padding: props.config.cellPadding || '12px 8px',
  borderBottom: props.config.cellBorder || '1px solid rgba(255, 255, 255, 0.1)',
  textAlign: column.align || 'left',
  verticalAlign: 'middle'
})

const getSortIcon = (key) => {
  if (sortField.value !== key) return 'Sort'
  return sortOrder.value === 'asc' ? 'SortUp' : 'SortDown'
}

const handleSort = (column) => {
  if (!column.sortable) return
  
  if (sortField.value === column.key) {
    if (sortOrder.value === 'asc') {
      sortOrder.value = 'desc'
    } else if (sortOrder.value === 'desc') {
      sortOrder.value = ''
      sortField.value = ''
    } else {
      sortOrder.value = 'asc'
    }
  } else {
    sortField.value = column.key
    sortOrder.value = 'asc'
  }
  
  emit('sort-change', { field: sortField.value, order: sortOrder.value })
}

const handleRowClick = (row, index) => {
  emit('row-click', { row, index })
}

const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
}

const handleCurrentChange = (page) => {
  currentPage.value = page
}

// 监听配置变化
watch(() => props.config, () => {
  if (props.config.pageSize) {
    pageSize.value = props.config.pageSize
  }
}, { immediate: true })
</script>

<style scoped>
.table-component {
  font-family: 'Microsoft YaHei', sans-serif;
}

.table-wrapper {
  border-radius: 4px;
  overflow: hidden;
}

.data-table {
  table-layout: fixed;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.sort-icon {
  margin-left: 4px;
  font-size: 12px;
  opacity: 0.6;
}

.sortable:hover .sort-icon {
  opacity: 1;
}

.row-hover:hover {
  background: rgba(0, 245, 255, 0.1) !important;
}

.cell-content {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.table-pagination {
  margin-top: 16px;
  display: flex;
  justify-content: center;
}

.empty-data {
  user-select: none;
}

.empty-data p {
  margin-top: 10px;
}

:deep(.el-pagination) {
  --el-pagination-text-color: #ffffff;
  --el-pagination-bg-color: transparent;
  --el-pagination-border-color: #2a3f5f;
}

:deep(.el-pagination .el-pager li) {
  background: rgba(255, 255, 255, 0.05);
  color: #ffffff;
  border: 1px solid #2a3f5f;
}

:deep(.el-pagination .el-pager li.is-active) {
  background: #00f5ff;
  color: #000000;
}
</style>
