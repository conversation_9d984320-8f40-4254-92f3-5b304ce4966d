<template>
  <div class="counter-component" :style="containerStyle">
    <div class="counter-content">
      <!-- 标题 -->
      <div v-if="showTitle" class="counter-title" :style="titleStyle">
        {{ title }}
      </div>
      
      <!-- 数字显示区域 -->
      <div class="counter-display" :style="displayStyle">
        <!-- 前缀 -->
        <span v-if="prefix" class="counter-prefix" :style="prefixStyle">{{ prefix }}</span>
        
        <!-- 数字翻牌器 -->
        <div class="counter-numbers">
          <div
            v-for="(digit, index) in displayDigits"
            :key="index"
            class="counter-digit"
            :style="digitStyle"
          >
            <div
              class="digit-container"
              :style="{ transform: `translateY(-${digit * 10}%)` }"
            >
              <div
                v-for="num in 10"
                :key="num - 1"
                class="digit-item"
                :style="digitItemStyle"
              >
                {{ num - 1 }}
              </div>
            </div>
          </div>
          
          <!-- 小数点 -->
          <span v-if="showDecimal" class="counter-decimal" :style="decimalStyle">.</span>
          
          <!-- 小数部分 -->
          <div
            v-for="(digit, index) in decimalDigits"
            :key="'decimal-' + index"
            class="counter-digit"
            :style="digitStyle"
          >
            <div
              class="digit-container"
              :style="{ transform: `translateY(-${digit * 10}%)` }"
            >
              <div
                v-for="num in 10"
                :key="num - 1"
                class="digit-item"
                :style="digitItemStyle"
              >
                {{ num - 1 }}
              </div>
            </div>
          </div>
        </div>
        
        <!-- 后缀 -->
        <span v-if="suffix" class="counter-suffix" :style="suffixStyle">{{ suffix }}</span>
      </div>
      
      <!-- 描述文字 -->
      <div v-if="showDescription" class="counter-description" :style="descriptionStyle">
        {{ description }}
      </div>
      
      <!-- 增长指示器 -->
      <div v-if="showGrowth && growthValue !== 0" class="counter-growth" :style="growthStyle">
        <el-icon>
          <component :is="growthValue > 0 ? 'ArrowUp' : 'ArrowDown'" />
        </el-icon>
        <span>{{ Math.abs(growthValue).toFixed(growthPrecision) }}{{ growthUnit }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'

const props = defineProps({
  config: { type: Object, required: true },
  data: { type: Object, default: () => ({}) }
})

// 响应式数据
const currentValue = ref(0)
const animationId = ref(null)

// 计算属性
const targetValue = computed(() => {
  return props.data.value !== undefined ? props.data.value : (props.config.value || 0)
})

const precision = computed(() => props.config.precision || 0)
const showDecimal = computed(() => precision.value > 0)

const title = computed(() => props.config.title || props.config.name || '')
const showTitle = computed(() => props.config.showTitle !== false && title.value)

const description = computed(() => props.config.description || '')
const showDescription = computed(() => props.config.showDescription !== false && description.value)

const prefix = computed(() => props.config.prefix || '')
const suffix = computed(() => props.config.suffix || '')

const growthValue = computed(() => props.data.growth || props.config.growth || 0)
const showGrowth = computed(() => props.config.showGrowth === true)
const growthPrecision = computed(() => props.config.growthPrecision || 1)
const growthUnit = computed(() => props.config.growthUnit || '%')

// 数字分解
const displayDigits = computed(() => {
  const integerPart = Math.floor(Math.abs(currentValue.value))
  const digits = integerPart.toString().padStart(props.config.minDigits || 1, '0').split('')
  return digits.map(d => parseInt(d))
})

const decimalDigits = computed(() => {
  if (!showDecimal.value) return []
  const decimalPart = (Math.abs(currentValue.value) % 1).toFixed(precision.value).slice(2)
  return decimalPart.split('').map(d => parseInt(d))
})

// 样式计算
const containerStyle = computed(() => ({
  background: props.config.backgroundColor || 'transparent',
  border: props.config.border || 'none',
  borderRadius: props.config.borderRadius || '8px',
  padding: props.config.padding || '20px',
  textAlign: props.config.textAlign || 'center'
}))

const titleStyle = computed(() => ({
  fontSize: (props.config.titleFontSize || 16) + 'px',
  color: props.config.titleColor || '#999',
  marginBottom: '10px',
  fontWeight: props.config.titleFontWeight || 'normal'
}))

const displayStyle = computed(() => ({
  display: 'flex',
  alignItems: 'baseline',
  justifyContent: props.config.textAlign || 'center',
  gap: '4px'
}))

const digitStyle = computed(() => ({
  width: (props.config.digitWidth || 40) + 'px',
  height: (props.config.digitHeight || 60) + 'px',
  background: props.config.digitBackground || 'rgba(0, 245, 255, 0.1)',
  border: props.config.digitBorder || '1px solid rgba(0, 245, 255, 0.3)',
  borderRadius: (props.config.digitBorderRadius || 4) + 'px',
  overflow: 'hidden',
  position: 'relative'
}))

const digitItemStyle = computed(() => ({
  height: '100%',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  fontSize: (props.config.fontSize || 32) + 'px',
  fontWeight: props.config.fontWeight || 'bold',
  color: props.config.color || '#00f5ff',
  fontFamily: props.config.fontFamily || 'monospace'
}))

const prefixStyle = computed(() => ({
  fontSize: (props.config.prefixFontSize || props.config.fontSize || 32) + 'px',
  color: props.config.prefixColor || props.config.color || '#00f5ff',
  fontWeight: props.config.fontWeight || 'bold'
}))

const suffixStyle = computed(() => ({
  fontSize: (props.config.suffixFontSize || props.config.fontSize || 32) + 'px',
  color: props.config.suffixColor || props.config.color || '#00f5ff',
  fontWeight: props.config.fontWeight || 'bold'
}))

const decimalStyle = computed(() => ({
  fontSize: (props.config.fontSize || 32) + 'px',
  color: props.config.color || '#00f5ff',
  fontWeight: props.config.fontWeight || 'bold'
}))

const descriptionStyle = computed(() => ({
  fontSize: (props.config.descriptionFontSize || 14) + 'px',
  color: props.config.descriptionColor || '#999',
  marginTop: '10px',
  fontWeight: props.config.descriptionFontWeight || 'normal'
}))

const growthStyle = computed(() => ({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  gap: '4px',
  marginTop: '8px',
  fontSize: '14px',
  color: growthValue.value > 0 ? '#00ff88' : '#ff4757',
  fontWeight: '500'
}))

// 数字动画
const animateToTarget = () => {
  const start = currentValue.value
  const target = targetValue.value
  const duration = props.config.animationDuration || 2000
  const startTime = Date.now()
  
  const animate = () => {
    const elapsed = Date.now() - startTime
    const progress = Math.min(elapsed / duration, 1)
    
    // 使用缓动函数
    const easeOutQuart = 1 - Math.pow(1 - progress, 4)
    currentValue.value = start + (target - start) * easeOutQuart
    
    if (progress < 1) {
      animationId.value = requestAnimationFrame(animate)
    } else {
      currentValue.value = target
    }
  }
  
  if (animationId.value) {
    cancelAnimationFrame(animationId.value)
  }
  
  animate()
}

// 监听目标值变化
watch(targetValue, () => {
  if (props.config.animation !== false) {
    animateToTarget()
  } else {
    currentValue.value = targetValue.value
  }
}, { immediate: true })

// 生命周期
onMounted(() => {
  if (props.config.animation !== false) {
    animateToTarget()
  } else {
    currentValue.value = targetValue.value
  }
})
</script>

<style scoped>
.counter-component {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.counter-content {
  width: 100%;
}

.counter-numbers {
  display: flex;
  align-items: baseline;
  gap: 2px;
}

.counter-digit {
  position: relative;
  display: inline-block;
}

.digit-container {
  transition: transform 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  will-change: transform;
}

.digit-item {
  user-select: none;
}

.counter-growth {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}
</style>
