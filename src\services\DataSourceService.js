/**
 * 数据源服务 - 处理数据源的实时更新和缓存
 */

import { getDataSourceData } from '@/api/dataSourceApi'

class DataSourceService {
  constructor() {
    this.cache = new Map() // 数据缓存
    this.timers = new Map() // 定时器管理
    this.subscribers = new Map() // 订阅者管理
    this.isRunning = false
  }

  /**
   * 启动数据源服务
   */
  start() {
    if (this.isRunning) return
    this.isRunning = true
    console.log('DataSource Service started')
  }

  /**
   * 停止数据源服务
   */
  stop() {
    if (!this.isRunning) return
    
    // 清除所有定时器
    this.timers.forEach(timer => clearInterval(timer))
    this.timers.clear()
    
    // 清除缓存
    this.cache.clear()
    
    // 清除订阅者
    this.subscribers.clear()
    
    this.isRunning = false
    console.log('DataSource Service stopped')
  }

  /**
   * 订阅数据源
   * @param {string} dataSourceId 数据源ID
   * @param {Function} callback 数据更新回调
   * @param {Object} options 选项
   */
  subscribe(dataSourceId, callback, options = {}) {
    if (!this.isRunning) {
      this.start()
    }

    const subscriberId = this.generateSubscriberId()
    
    // 添加订阅者
    if (!this.subscribers.has(dataSourceId)) {
      this.subscribers.set(dataSourceId, new Map())
    }
    this.subscribers.get(dataSourceId).set(subscriberId, {
      callback,
      options
    })

    // 立即获取数据
    this.fetchData(dataSourceId).then(data => {
      callback(data, null)
    }).catch(error => {
      callback(null, error)
    })

    // 设置定时更新
    this.setupAutoUpdate(dataSourceId)

    return subscriberId
  }

  /**
   * 取消订阅
   * @param {string} dataSourceId 数据源ID
   * @param {string} subscriberId 订阅者ID
   */
  unsubscribe(dataSourceId, subscriberId) {
    if (this.subscribers.has(dataSourceId)) {
      this.subscribers.get(dataSourceId).delete(subscriberId)
      
      // 如果没有订阅者了，清除定时器
      if (this.subscribers.get(dataSourceId).size === 0) {
        this.subscribers.delete(dataSourceId)
        this.clearAutoUpdate(dataSourceId)
      }
    }
  }

  /**
   * 手动刷新数据源
   * @param {string} dataSourceId 数据源ID
   */
  async refresh(dataSourceId) {
    try {
      const data = await this.fetchData(dataSourceId, true)
      this.notifySubscribers(dataSourceId, data, null)
      return data
    } catch (error) {
      this.notifySubscribers(dataSourceId, null, error)
      throw error
    }
  }

  /**
   * 获取缓存数据
   * @param {string} dataSourceId 数据源ID
   */
  getCachedData(dataSourceId) {
    const cached = this.cache.get(dataSourceId)
    return cached ? cached.data : null
  }

  /**
   * 获取数据源状态
   * @param {string} dataSourceId 数据源ID
   */
  getStatus(dataSourceId) {
    const cached = this.cache.get(dataSourceId)
    return cached ? {
      lastUpdate: cached.timestamp,
      hasError: cached.hasError,
      error: cached.error,
      isLoading: cached.isLoading
    } : null
  }

  /**
   * 私有方法：获取数据
   */
  async fetchData(dataSourceId, forceRefresh = false) {
    const cached = this.cache.get(dataSourceId)
    
    // 如果有缓存且不强制刷新，返回缓存数据
    if (cached && !forceRefresh && !this.isCacheExpired(cached)) {
      return cached.data
    }

    // 设置加载状态
    this.updateCache(dataSourceId, null, false, null, true)

    try {
      const response = await getDataSourceData(dataSourceId)
      
      if (response.code === 200) {
        const data = response.data.data || []
        this.updateCache(dataSourceId, data, false, null, false)
        return data
      } else {
        throw new Error(response.msg || '获取数据失败')
      }
    } catch (error) {
      this.updateCache(dataSourceId, null, true, error.message, false)
      throw error
    }
  }

  /**
   * 私有方法：设置自动更新
   */
  setupAutoUpdate(dataSourceId) {
    if (this.timers.has(dataSourceId)) {
      return // 已经设置了定时器
    }

    // 这里应该从数据源配置中获取更新间隔
    // 暂时使用默认值
    const updateInterval = 30000 // 30秒

    if (updateInterval > 0) {
      const timer = setInterval(async () => {
        try {
          const data = await this.fetchData(dataSourceId, true)
          this.notifySubscribers(dataSourceId, data, null)
        } catch (error) {
          this.notifySubscribers(dataSourceId, null, error)
        }
      }, updateInterval)

      this.timers.set(dataSourceId, timer)
    }
  }

  /**
   * 私有方法：清除自动更新
   */
  clearAutoUpdate(dataSourceId) {
    if (this.timers.has(dataSourceId)) {
      clearInterval(this.timers.get(dataSourceId))
      this.timers.delete(dataSourceId)
    }
  }

  /**
   * 私有方法：通知订阅者
   */
  notifySubscribers(dataSourceId, data, error) {
    if (this.subscribers.has(dataSourceId)) {
      this.subscribers.get(dataSourceId).forEach(subscriber => {
        try {
          subscriber.callback(data, error)
        } catch (callbackError) {
          console.error('Subscriber callback error:', callbackError)
        }
      })
    }
  }

  /**
   * 私有方法：更新缓存
   */
  updateCache(dataSourceId, data, hasError, error, isLoading) {
    this.cache.set(dataSourceId, {
      data,
      timestamp: Date.now(),
      hasError,
      error,
      isLoading
    })
  }

  /**
   * 私有方法：检查缓存是否过期
   */
  isCacheExpired(cached) {
    const maxAge = 5 * 60 * 1000 // 5分钟
    return Date.now() - cached.timestamp > maxAge
  }

  /**
   * 私有方法：生成订阅者ID
   */
  generateSubscriberId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2)
  }

  /**
   * 获取所有活跃的数据源
   */
  getActiveDataSources() {
    return Array.from(this.subscribers.keys())
  }

  /**
   * 获取数据源订阅者数量
   */
  getSubscriberCount(dataSourceId) {
    return this.subscribers.has(dataSourceId) 
      ? this.subscribers.get(dataSourceId).size 
      : 0
  }

  /**
   * 清理过期缓存
   */
  cleanupExpiredCache() {
    const now = Date.now()
    const maxAge = 30 * 60 * 1000 // 30分钟

    for (const [dataSourceId, cached] of this.cache.entries()) {
      if (now - cached.timestamp > maxAge) {
        this.cache.delete(dataSourceId)
      }
    }
  }
}

// 创建单例实例
const dataSourceService = new DataSourceService()

// 定期清理过期缓存
setInterval(() => {
  dataSourceService.cleanupExpiredCache()
}, 10 * 60 * 1000) // 每10分钟清理一次

export default dataSourceService
