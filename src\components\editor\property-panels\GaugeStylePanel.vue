<template>
  <div class="gauge-style-panel">
    <div class="property-section">
      <h4>仪表盘设置</h4>
      <el-form label-width="80px" size="small">
        <el-form-item label="标题">
          <el-input 
            v-model="styleData.title" 
            placeholder="请输入标题"
            @change="updateStyle"
          />
        </el-form-item>
        <el-form-item label="最小值">
          <el-input-number 
            v-model="styleData.min" 
            :step="1"
            @change="updateStyle"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="最大值">
          <el-input-number 
            v-model="styleData.max" 
            :step="1"
            @change="updateStyle"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="单位">
          <el-input 
            v-model="styleData.unit" 
            placeholder="如: %、万元"
            @change="updateStyle"
          />
        </el-form-item>
        <el-form-item label="精度">
          <el-input-number 
            v-model="styleData.precision" 
            :min="0"
            :max="5"
            :step="1"
            @change="updateStyle"
            style="width: 100%"
          />
        </el-form-item>
      </el-form>
    </div>
    
    <div class="property-section">
      <h4>外观样式</h4>
      <el-form label-width="80px" size="small">
        <el-form-item label="半径">
          <el-input 
            v-model="styleData.radius" 
            placeholder="如: 80%"
            @change="updateStyle"
          />
        </el-form-item>
        <el-form-item label="起始角度">
          <el-input-number 
            v-model="styleData.startAngle" 
            :min="0"
            :max="360"
            :step="1"
            @change="updateStyle"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="结束角度">
          <el-input-number 
            v-model="styleData.endAngle" 
            :min="-360"
            :max="360"
            :step="1"
            @change="updateStyle"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="轴线宽度">
          <el-input-number 
            v-model="styleData.axisLineWidth" 
            :min="1"
            :max="50"
            :step="1"
            @change="updateStyle"
            style="width: 100%"
          />
        </el-form-item>
      </el-form>
    </div>
    
    <div class="property-section">
      <h4>指针样式</h4>
      <el-form label-width="80px" size="small">
        <el-form-item label="指针长度">
          <el-input 
            v-model="styleData.pointerLength" 
            placeholder="如: 75%"
            @change="updateStyle"
          />
        </el-form-item>
        <el-form-item label="指针宽度">
          <el-input-number 
            v-model="styleData.pointerWidth" 
            :min="1"
            :max="20"
            :step="1"
            @change="updateStyle"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="指针颜色">
          <el-color-picker 
            v-model="styleData.pointerColor" 
            @change="updateStyle"
          />
        </el-form-item>
      </el-form>
    </div>
    
    <div class="property-section">
      <h4>文字样式</h4>
      <el-form label-width="80px" size="small">
        <el-form-item label="标题大小">
          <el-input-number 
            v-model="styleData.titleFontSize" 
            :min="8"
            :max="48"
            :step="1"
            @change="updateStyle"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="标题颜色">
          <el-color-picker 
            v-model="styleData.titleColor" 
            @change="updateStyle"
          />
        </el-form-item>
        <el-form-item label="数值大小">
          <el-input-number 
            v-model="styleData.valueFontSize" 
            :min="8"
            :max="48"
            :step="1"
            @change="updateStyle"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="数值颜色">
          <el-color-picker 
            v-model="styleData.valueColor" 
            @change="updateStyle"
          />
        </el-form-item>
      </el-form>
    </div>
    
    <div class="property-section">
      <h4>动画效果</h4>
      <el-form label-width="80px" size="small">
        <el-form-item label="启用动画">
          <el-switch 
            v-model="styleData.animation" 
            @change="updateStyle"
          />
        </el-form-item>
        <el-form-item label="动画时长" v-if="styleData.animation">
          <div style="display: flex; align-items: center;">
            <el-input-number 
              v-model="styleData.animationDuration" 
              :min="100"
              :max="10000"
              :step="100"
              @change="updateStyle"
              style="width: 100%"
            />
            <span style="margin-left: 8px; color: #999; font-size: 12px;">毫秒</span>
          </div>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import { reactive, watch } from 'vue'

const props = defineProps({
  component: { type: Object, required: true }
})

const emit = defineEmits(['update'])

// 响应式数据
const styleData = reactive({
  title: '',
  min: 0,
  max: 100,
  unit: '%',
  precision: 1,
  radius: '80%',
  startAngle: 225,
  endAngle: -45,
  axisLineWidth: 10,
  pointerLength: '75%',
  pointerWidth: 6,
  pointerColor: '#00f5ff',
  titleFontSize: 16,
  titleColor: '#ffffff',
  valueFontSize: 24,
  valueColor: '#00f5ff',
  animation: true,
  animationDuration: 2000
})

// 监听组件变化
watch(() => props.component, (newComponent) => {
  if (newComponent && newComponent.config) {
    const config = newComponent.config
    Object.assign(styleData, {
      title: config.title || '',
      min: config.min || 0,
      max: config.max || 100,
      unit: config.unit || '%',
      precision: config.precision || 1,
      radius: config.radius || '80%',
      startAngle: config.startAngle || 225,
      endAngle: config.endAngle || -45,
      axisLineWidth: config.axisLineWidth || 10,
      pointerLength: config.pointerLength || '75%',
      pointerWidth: config.pointerWidth || 6,
      pointerColor: config.pointerColor || '#00f5ff',
      titleFontSize: config.titleFontSize || 16,
      titleColor: config.titleColor || '#ffffff',
      valueFontSize: config.valueFontSize || 24,
      valueColor: config.valueColor || '#00f5ff',
      animation: config.animation !== false,
      animationDuration: config.animationDuration || 2000
    })
  }
}, { immediate: true })

// 方法
const updateStyle = () => {
  emit('update', { 
    config: { 
      ...props.component.config,
      ...styleData 
    }
  })
}
</script>

<style scoped>
.gauge-style-panel {
  width: 100%;
}

.property-section {
  margin-bottom: 1.5rem;
}

.property-section h4 {
  margin: 0 0 1rem 0;
  font-size: 0.9rem;
  color: #00f5ff;
  border-bottom: 1px solid #2a3f5f;
  padding-bottom: 0.5rem;
}
</style>
