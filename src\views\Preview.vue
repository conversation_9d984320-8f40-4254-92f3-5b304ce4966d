<template>
  <div class="preview-container">
    <!-- 顶部控制栏 -->
    <div class="preview-controls" v-show="showControls">
      <div class="controls-left">
        <el-button @click="goBack" size="small">
          <el-icon><ArrowLeft /></el-icon>
          返回编辑
        </el-button>
        <span class="project-title">{{ projectData.name }}</span>
      </div>
      <div class="controls-right">
        <el-button @click="toggleFullscreen" size="small">
          <el-icon><FullScreen /></el-icon>
          {{ isFullscreen ? '退出全屏' : '全屏预览' }}
        </el-button>
        <el-button @click="refreshData" size="small">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
      </div>
    </div>

    <!-- 预览画布 -->
    <div 
      class="preview-canvas"
      :style="canvasStyle"
      @mousemove="handleMouseMove"
      @mouseleave="hideControls"
    >
      <!-- 背景 -->
      <div class="canvas-background" :style="backgroundStyle"></div>
      
      <!-- 组件渲染区域 -->
      <div
        v-for="component in projectData.components"
        :key="component.id"
        class="preview-component"
        :style="getComponentStyle(component)"
      >
        <!-- 这里将来会根据组件类型渲染不同的组件 -->
        <component 
          :is="getComponentType(component.type)"
          :config="component"
          :data="getComponentData(component.id)"
        />
      </div>

      <!-- 加载状态 -->
      <div v-if="loading" class="loading-overlay">
        <el-icon class="loading-icon" size="40"><Loading /></el-icon>
        <p>加载中...</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { getProjectDetail } from '@/api/projectApi'

const router = useRouter()
const route = useRoute()

// 响应式数据
const loading = ref(true)
const showControls = ref(true)
const isFullscreen = ref(false)
const controlsTimer = ref(null)

const projectData = reactive({
  id: null,
  name: '预览项目',
  width: 1920,
  height: 1080,
  backgroundColor: '#0a1a3a',
  components: []
})

// 计算属性
const canvasStyle = computed(() => {
  const windowWidth = window.innerWidth
  const windowHeight = window.innerHeight
  const canvasRatio = projectData.width / projectData.height
  const windowRatio = windowWidth / windowHeight
  
  let scale = 1
  if (canvasRatio > windowRatio) {
    // 画布更宽，以宽度为准
    scale = windowWidth / projectData.width
  } else {
    // 画布更高，以高度为准
    scale = (windowHeight - (showControls.value ? 60 : 0)) / projectData.height
  }
  
  return {
    width: projectData.width + 'px',
    height: projectData.height + 'px',
    transform: `scale(${scale})`,
    transformOrigin: 'center top'
  }
})

const backgroundStyle = computed(() => ({
  background: projectData.backgroundColor,
  width: '100%',
  height: '100%',
  position: 'absolute',
  top: 0,
  left: 0
}))

// 方法
const loadProjectData = async () => {
  const projectId = route.params.id
  if (!projectId) {
    ElMessage.error('项目ID不存在')
    router.push('/projects')
    return
  }
  
  try {
    loading.value = true
    const res = await getProjectDetail(projectId)
    if (res.code === 200) {
      Object.assign(projectData, res.data)
      // 模拟一些组件数据
      projectData.components = [
        {
          id: 1,
          type: 'text-title',
          name: '标题组件',
          x: 100,
          y: 50,
          width: 400,
          height: 60,
          zIndex: 1,
          config: {
            text: '智慧城市数据大屏',
            fontSize: 32,
            color: '#ffffff',
            fontWeight: 'bold'
          }
        },
        {
          id: 2,
          type: 'bar-chart',
          name: '柱状图',
          x: 100,
          y: 150,
          width: 500,
          height: 300,
          zIndex: 1,
          config: {
            title: '月度销售数据',
            data: [
              { name: '一月', value: 120 },
              { name: '二月', value: 200 },
              { name: '三月', value: 150 },
              { name: '四月', value: 80 },
              { name: '五月', value: 70 },
              { name: '六月', value: 110 }
            ]
          }
        },
        {
          id: 3,
          type: 'pie-chart',
          name: '饼图',
          x: 700,
          y: 150,
          width: 400,
          height: 300,
          zIndex: 1,
          config: {
            title: '用户来源分析',
            data: [
              { name: '直接访问', value: 335 },
              { name: '邮件营销', value: 310 },
              { name: '联盟广告', value: 234 },
              { name: '视频广告', value: 135 },
              { name: '搜索引擎', value: 1548 }
            ]
          }
        }
      ]
    } else {
      ElMessage.error(res.msg || '加载项目失败')
      router.push('/projects')
    }
  } catch (error) {
    ElMessage.error('加载项目失败')
    router.push('/projects')
  } finally {
    loading.value = false
  }
}

const getComponentStyle = (component) => ({
  position: 'absolute',
  left: component.x + 'px',
  top: component.y + 'px',
  width: component.width + 'px',
  height: component.height + 'px',
  zIndex: component.zIndex
})

const getComponentType = (type) => {
  // 根据组件类型返回对应的组件名称
  // 这里先返回一个通用的预览组件
  return 'PreviewComponent'
}

const getComponentData = (componentId) => {
  // 获取组件的实时数据
  // 这里可以根据组件ID从数据源获取数据
  return {}
}

const goBack = () => {
  const projectId = route.params.id
  router.push(`/editor/${projectId}`)
}

const toggleFullscreen = () => {
  if (!isFullscreen.value) {
    // 进入全屏
    if (document.documentElement.requestFullscreen) {
      document.documentElement.requestFullscreen()
    }
  } else {
    // 退出全屏
    if (document.exitFullscreen) {
      document.exitFullscreen()
    }
  }
}

const refreshData = () => {
  ElMessage.success('数据刷新成功')
  // 这里可以重新加载组件数据
}

const handleMouseMove = () => {
  showControls.value = true
  clearTimeout(controlsTimer.value)
  controlsTimer.value = setTimeout(() => {
    if (isFullscreen.value) {
      showControls.value = false
    }
  }, 3000)
}

const hideControls = () => {
  if (isFullscreen.value) {
    clearTimeout(controlsTimer.value)
    controlsTimer.value = setTimeout(() => {
      showControls.value = false
    }, 1000)
  }
}

const handleFullscreenChange = () => {
  isFullscreen.value = !!document.fullscreenElement
  if (!isFullscreen.value) {
    showControls.value = true
    clearTimeout(controlsTimer.value)
  }
}

// 生命周期
onMounted(() => {
  loadProjectData()
  document.addEventListener('fullscreenchange', handleFullscreenChange)
})

onUnmounted(() => {
  document.removeEventListener('fullscreenchange', handleFullscreenChange)
  clearTimeout(controlsTimer.value)
})
</script>

<script>
// 通用预览组件
const PreviewComponent = {
  props: ['config', 'data'],
  template: `
    <div class="preview-component-content">
      <div class="component-title" v-if="config.config?.title">
        {{ config.config.title }}
      </div>
      <div class="component-body">
        <div v-if="config.type === 'text-title'" class="text-component">
          <div :style="textStyle">{{ config.config?.text || config.name }}</div>
        </div>
        <div v-else-if="config.type.includes('chart')" class="chart-component">
          <div class="chart-placeholder">
            <el-icon size="40"><TrendCharts /></el-icon>
            <p>{{ config.name }}</p>
          </div>
        </div>
        <div v-else class="default-component">
          <div class="component-placeholder">
            {{ config.name }}
          </div>
        </div>
      </div>
    </div>
  `,
  computed: {
    textStyle() {
      const config = this.config.config || {}
      return {
        fontSize: (config.fontSize || 16) + 'px',
        color: config.color || '#ffffff',
        fontWeight: config.fontWeight || 'normal',
        width: '100%',
        height: '100%',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }
    }
  }
}

export default {
  components: {
    PreviewComponent
  }
}
</script>

<style scoped>
.preview-container {
  width: 100vw;
  height: 100vh;
  background: #000000;
  overflow: hidden;
  position: relative;
}

.preview-controls {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 60px;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 2rem;
  z-index: 1000;
  transition: transform 0.3s ease;
}

.controls-left {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.project-title {
  color: #ffffff;
  font-size: 1.1rem;
  font-weight: 600;
}

.controls-right {
  display: flex;
  gap: 0.5rem;
}

.preview-canvas {
  position: absolute;
  top: 50%;
  left: 50%;
  transform-origin: center center;
  margin-top: 30px; /* 为控制栏留出空间 */
}

.canvas-background {
  /* 背景样式 */
}

.preview-component {
  /* 组件容器样式 */
}

.preview-component-content {
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  overflow: hidden;
}

.component-title {
  background: rgba(0, 245, 255, 0.1);
  color: #00f5ff;
  padding: 0.5rem 1rem;
  font-size: 0.9rem;
  font-weight: 600;
  border-bottom: 1px solid rgba(0, 245, 255, 0.2);
}

.component-body {
  padding: 1rem;
  height: calc(100% - 40px);
}

.text-component {
  width: 100%;
  height: 100%;
}

.chart-component {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-placeholder {
  text-align: center;
  color: rgba(255, 255, 255, 0.6);
}

.chart-placeholder p {
  margin-top: 0.5rem;
  font-size: 0.9rem;
}

.default-component {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.component-placeholder {
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.9rem;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  z-index: 100;
}

.loading-icon {
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 全屏模式下隐藏控制栏 */
.preview-container:fullscreen .preview-controls {
  transform: translateY(-100%);
}

.preview-container:fullscreen .preview-canvas {
  margin-top: 0;
}
</style>
