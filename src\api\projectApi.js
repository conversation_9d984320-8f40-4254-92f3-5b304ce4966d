/**
 * 项目管理相关API
 */

// 模拟项目数据
const mockProjects = [
  {
    id: 1,
    name: '智慧城市大屏',
    description: '展示城市各项数据指标的综合大屏',
    thumbnail: '/images/project1.jpg',
    createTime: '2024-01-15 10:30:00',
    updateTime: '2024-01-20 14:20:00',
    status: 'published',
    author: '张三',
    width: 1920,
    height: 1080,
    backgroundColor: '#0a1a3a'
  },
  {
    id: 2,
    name: '销售数据分析',
    description: '实时展示销售业绩和趋势分析',
    thumbnail: '/images/project2.jpg',
    createTime: '2024-01-18 09:15:00',
    updateTime: '2024-01-22 16:45:00',
    status: 'draft',
    author: '李四',
    width: 1920,
    height: 1080,
    backgroundColor: '#1a1a2e'
  },
  {
    id: 3,
    name: '运营监控中心',
    description: '系统运营状态实时监控大屏',
    thumbnail: '/images/project3.jpg',
    createTime: '2024-01-20 11:20:00',
    updateTime: '2024-01-23 13:30:00',
    status: 'published',
    author: '王五',
    width: 1920,
    height: 1080,
    backgroundColor: '#16213e'
  }
]

/**
 * 获取项目列表
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码
 * @param {number} params.pageSize - 每页数量
 * @param {string} params.keyword - 搜索关键词
 * @param {string} params.status - 项目状态
 */
export function getProjectList(params = {}) {
  const { page = 1, pageSize = 10, keyword = '', status = '' } = params
  
  return new Promise(resolve => {
    setTimeout(() => {
      let filteredProjects = [...mockProjects]
      
      // 关键词搜索
      if (keyword) {
        filteredProjects = filteredProjects.filter(project => 
          project.name.includes(keyword) || project.description.includes(keyword)
        )
      }
      
      // 状态筛选
      if (status) {
        filteredProjects = filteredProjects.filter(project => project.status === status)
      }
      
      // 分页
      const start = (page - 1) * pageSize
      const end = start + pageSize
      const list = filteredProjects.slice(start, end)
      
      resolve({
        code: 200,
        msg: '获取成功',
        data: {
          list,
          total: filteredProjects.length,
          page,
          pageSize
        }
      })
    }, 300)
  })
}

/**
 * 获取项目详情
 * @param {number} id - 项目ID
 */
export function getProjectDetail(id) {
  return new Promise(resolve => {
    setTimeout(() => {
      const project = mockProjects.find(p => p.id == id)
      if (project) {
        resolve({
          code: 200,
          msg: '获取成功',
          data: {
            ...project,
            components: [], // 组件数据
            config: {
              grid: { show: true, size: 20 },
              ruler: { show: true },
              zoom: 100
            }
          }
        })
      } else {
        resolve({
          code: 404,
          msg: '项目不存在',
          data: null
        })
      }
    }, 200)
  })
}

/**
 * 创建项目
 * @param {Object} projectData - 项目数据
 */
export function createProject(projectData) {
  return new Promise(resolve => {
    setTimeout(() => {
      const newProject = {
        id: Date.now(),
        ...projectData,
        createTime: new Date().toLocaleString(),
        updateTime: new Date().toLocaleString(),
        status: 'draft',
        author: '当前用户'
      }
      
      mockProjects.unshift(newProject)
      
      resolve({
        code: 200,
        msg: '创建成功',
        data: newProject
      })
    }, 500)
  })
}

/**
 * 更新项目
 * @param {number} id - 项目ID
 * @param {Object} projectData - 项目数据
 */
export function updateProject(id, projectData) {
  return new Promise(resolve => {
    setTimeout(() => {
      const index = mockProjects.findIndex(p => p.id == id)
      if (index !== -1) {
        mockProjects[index] = {
          ...mockProjects[index],
          ...projectData,
          updateTime: new Date().toLocaleString()
        }
        
        resolve({
          code: 200,
          msg: '更新成功',
          data: mockProjects[index]
        })
      } else {
        resolve({
          code: 404,
          msg: '项目不存在',
          data: null
        })
      }
    }, 400)
  })
}

/**
 * 删除项目
 * @param {number} id - 项目ID
 */
export function deleteProject(id) {
  return new Promise(resolve => {
    setTimeout(() => {
      const index = mockProjects.findIndex(p => p.id == id)
      if (index !== -1) {
        mockProjects.splice(index, 1)
        resolve({
          code: 200,
          msg: '删除成功',
          data: null
        })
      } else {
        resolve({
          code: 404,
          msg: '项目不存在',
          data: null
        })
      }
    }, 300)
  })
}

/**
 * 复制项目
 * @param {number} id - 项目ID
 */
export function copyProject(id) {
  return new Promise(resolve => {
    setTimeout(() => {
      const project = mockProjects.find(p => p.id == id)
      if (project) {
        const newProject = {
          ...project,
          id: Date.now(),
          name: `${project.name}_副本`,
          createTime: new Date().toLocaleString(),
          updateTime: new Date().toLocaleString(),
          status: 'draft'
        }
        
        mockProjects.unshift(newProject)
        
        resolve({
          code: 200,
          msg: '复制成功',
          data: newProject
        })
      } else {
        resolve({
          code: 404,
          msg: '项目不存在',
          data: null
        })
      }
    }, 400)
  })
}

/**
 * 发布项目
 * @param {number} id - 项目ID
 */
export function publishProject(id) {
  return new Promise(resolve => {
    setTimeout(() => {
      const index = mockProjects.findIndex(p => p.id == id)
      if (index !== -1) {
        mockProjects[index].status = 'published'
        mockProjects[index].updateTime = new Date().toLocaleString()
        
        resolve({
          code: 200,
          msg: '发布成功',
          data: mockProjects[index]
        })
      } else {
        resolve({
          code: 404,
          msg: '项目不存在',
          data: null
        })
      }
    }, 600)
  })
}
