<template>
  <div class="chart-data-panel">
    <div class="property-section">
      <h4>数据源配置</h4>
      <el-form label-width="80px" size="small">
        <el-form-item label="数据类型">
          <el-radio-group v-model="dataConfig.dataType" @change="updateData">
            <el-radio label="static">静态数据</el-radio>
            <el-radio label="dynamic">动态数据</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <template v-if="dataConfig.dataType === 'static'">
          <el-form-item label="数据编辑">
            <el-button @click="showDataEditor = true" style="width: 100%">
              <el-icon><Edit /></el-icon>
              编辑数据
            </el-button>
          </el-form-item>
        </template>
        
        <template v-if="dataConfig.dataType === 'dynamic'">
          <el-form-item label="数据源">
            <el-select 
              v-model="dataConfig.dataSourceId" 
              placeholder="选择数据源"
              style="width: 100%"
              @change="updateData"
            >
              <el-option 
                v-for="source in dataSources"
                :key="source.id"
                :label="source.name"
                :value="source.id"
              />
            </el-select>
          </el-form-item>
          
          <el-form-item label="名称字段" v-if="dataConfig.dataSourceId">
            <el-select 
              v-model="dataConfig.nameField" 
              placeholder="选择名称字段"
              style="width: 100%"
              @change="updateData"
            >
              <el-option 
                v-for="field in availableFields"
                :key="field"
                :label="field"
                :value="field"
              />
            </el-select>
          </el-form-item>
          
          <el-form-item label="数值字段" v-if="dataConfig.dataSourceId">
            <el-select 
              v-model="dataConfig.valueField" 
              placeholder="选择数值字段"
              style="width: 100%"
              @change="updateData"
            >
              <el-option 
                v-for="field in availableFields"
                :key="field"
                :label="field"
                :value="field"
              />
            </el-select>
          </el-form-item>
          
          <el-form-item label="数据过滤">
            <el-input 
              v-model="dataConfig.filter" 
              placeholder="如: value > 100"
              @change="updateData"
            />
          </el-form-item>
          
          <el-form-item label="刷新间隔">
            <div style="display: flex; align-items: center;">
              <el-input-number 
                v-model="dataConfig.refreshInterval" 
                :min="0" 
                :max="3600"
                :step="1"
                @change="updateData"
                style="width: 100%"
              />
              <span style="margin-left: 8px; color: #999; font-size: 12px;">秒</span>
            </div>
          </el-form-item>
        </template>
      </el-form>
    </div>
    
    <div class="property-section">
      <h4>数据预览</h4>
      <div class="data-preview">
        <div class="preview-label">当前数据：</div>
        <div class="preview-content">
          <div v-if="previewData.length > 0" class="data-table">
            <div class="table-header">
              <span>名称</span>
              <span>数值</span>
            </div>
            <div 
              v-for="(item, index) in previewData.slice(0, 5)"
              :key="index"
              class="table-row"
            >
              <span>{{ item.name }}</span>
              <span>{{ item.value }}</span>
            </div>
            <div v-if="previewData.length > 5" class="table-more">
              还有 {{ previewData.length - 5 }} 条数据...
            </div>
          </div>
          <div v-else class="no-data">暂无数据</div>
        </div>
        <el-button size="small" @click="refreshPreview" style="margin-top: 10px;">
          <el-icon><Refresh /></el-icon>
          刷新预览
        </el-button>
      </div>
    </div>

    <!-- 数据编辑器对话框 -->
    <el-dialog 
      v-model="showDataEditor" 
      title="编辑图表数据" 
      width="600px"
      :before-close="handleDataEditorClose"
    >
      <div class="data-editor">
        <div class="editor-toolbar">
          <el-button size="small" @click="addDataRow">
            <el-icon><Plus /></el-icon>
            添加行
          </el-button>
          <el-button size="small" @click="clearData">
            <el-icon><Delete /></el-icon>
            清空数据
          </el-button>
          <el-button size="small" @click="importData">
            <el-icon><Upload /></el-icon>
            导入数据
          </el-button>
        </div>
        
        <div class="data-table-editor">
          <div class="table-header">
            <span>名称</span>
            <span>数值</span>
            <span>操作</span>
          </div>
          <div 
            v-for="(item, index) in editingData"
            :key="index"
            class="table-row"
          >
            <el-input v-model="item.name" size="small" />
            <el-input-number v-model="item.value" size="small" style="width: 100%" />
            <el-button 
              size="small" 
              type="danger" 
              @click="removeDataRow(index)"
              :icon="Delete"
            />
          </div>
        </div>
      </div>
      
      <template #footer>
        <el-button @click="showDataEditor = false">取消</el-button>
        <el-button type="primary" @click="saveData">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted, onUnmounted } from 'vue'
import { getDataSourceList } from '@/api/dataSourceApi'
import dataSourceService from '@/services/DataSourceService'

const props = defineProps({
  component: { type: Object, required: true }
})

const emit = defineEmits(['update'])

// 响应式数据
const dataSources = ref([])
const showDataEditor = ref(false)
const editingData = ref([])
const subscriberId = ref(null)
const realTimeData = ref([])

const dataConfig = reactive({
  dataType: 'static',
  staticData: [],
  dataSourceId: null,
  nameField: '',
  valueField: '',
  filter: '',
  refreshInterval: 0
})

// 计算属性
const availableFields = computed(() => {
  // 这里应该根据选中的数据源返回可用字段
  return ['name', 'value', 'label', 'count', 'amount', 'category']
})

const previewData = computed(() => {
  if (dataConfig.dataType === 'static') {
    return dataConfig.staticData
  } else if (dataConfig.dataType === 'dynamic' && realTimeData.value.length > 0) {
    // 使用实时数据
    return realTimeData.value
  } else {
    // 默认示例数据
    return [
      { name: '示例1', value: 100 },
      { name: '示例2', value: 200 },
      { name: '示例3', value: 150 }
    ]
  }
})

// 监听组件变化
watch(() => props.component, (newComponent) => {
  if (newComponent) {
    const config = newComponent.dataConfig || {}
    Object.assign(dataConfig, {
      dataType: config.dataType || 'static',
      staticData: config.staticData || newComponent.data || [],
      dataSourceId: config.dataSourceId || null,
      nameField: config.nameField || '',
      valueField: config.valueField || '',
      filter: config.filter || '',
      refreshInterval: config.refreshInterval || 0
    })
  }
}, { immediate: true })

// 方法
const loadDataSources = async () => {
  try {
    const res = await getDataSourceList()
    if (res.code === 200) {
      dataSources.value = res.data.list
    }
  } catch (error) {
    console.error('加载数据源失败:', error)
  }
}

const updateData = () => {
  emit('update', {
    dataConfig: { ...dataConfig },
    data: dataConfig.dataType === 'static' ? dataConfig.staticData : realTimeData.value
  })

  // 如果是动态数据，设置数据源订阅
  if (dataConfig.dataType === 'dynamic' && dataConfig.dataSourceId) {
    subscribeToDataSource()
  } else {
    unsubscribeFromDataSource()
  }
}

const subscribeToDataSource = () => {
  if (!dataConfig.dataSourceId) return

  // 取消之前的订阅
  unsubscribeFromDataSource()

  // 订阅新的数据源
  subscriberId.value = dataSourceService.subscribe(
    dataConfig.dataSourceId,
    (data, error) => {
      if (error) {
        console.error('数据源更新失败:', error)
        return
      }

      if (data && Array.isArray(data)) {
        // 根据字段映射处理数据
        realTimeData.value = processDataWithMapping(data)

        // 自动更新组件数据
        emit('update', {
          dataConfig: { ...dataConfig },
          data: realTimeData.value
        })
      }
    }
  )
}

const unsubscribeFromDataSource = () => {
  if (subscriberId.value && dataConfig.dataSourceId) {
    dataSourceService.unsubscribe(dataConfig.dataSourceId, subscriberId.value)
    subscriberId.value = null
  }
}

const processDataWithMapping = (rawData) => {
  if (!dataConfig.nameField || !dataConfig.valueField) {
    return rawData
  }

  return rawData.map(item => ({
    name: item[dataConfig.nameField] || item.name || '',
    value: item[dataConfig.valueField] || item.value || 0,
    ...item // 保留原始数据的其他字段
  })).filter(item => item.name && item.value !== undefined)
}

const refreshPreview = () => {
  // 刷新数据预览
  console.log('刷新数据预览')
}

const addDataRow = () => {
  editingData.value.push({ name: '', value: 0 })
}

const removeDataRow = (index) => {
  editingData.value.splice(index, 1)
}

const clearData = () => {
  editingData.value = []
}

const importData = () => {
  // 导入数据功能
  console.log('导入数据')
}

const saveData = () => {
  dataConfig.staticData = [...editingData.value]
  updateData()
  showDataEditor.value = false
}

const handleDataEditorClose = () => {
  // 恢复编辑前的数据
  editingData.value = [...dataConfig.staticData]
}

// 监听数据编辑器显示
watch(showDataEditor, (show) => {
  if (show) {
    editingData.value = [...dataConfig.staticData]
  }
})

// 生命周期
onMounted(() => {
  loadDataSources()

  // 如果已经配置了动态数据源，立即订阅
  if (dataConfig.dataType === 'dynamic' && dataConfig.dataSourceId) {
    subscribeToDataSource()
  }
})

onUnmounted(() => {
  // 组件销毁时取消订阅
  unsubscribeFromDataSource()
})
</script>

<style scoped>
.chart-data-panel {
  width: 100%;
}

.property-section {
  margin-bottom: 1.5rem;
}

.property-section h4 {
  margin: 0 0 1rem 0;
  font-size: 0.9rem;
  color: #00f5ff;
  border-bottom: 1px solid #2a3f5f;
  padding-bottom: 0.5rem;
}

.data-preview {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid #2a3f5f;
  border-radius: 4px;
  padding: 1rem;
}

.preview-label {
  font-size: 0.8rem;
  color: #999;
  margin-bottom: 0.5rem;
}

.preview-content {
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid #2a3f5f;
  border-radius: 4px;
  padding: 0.5rem;
  color: #ffffff;
  min-height: 100px;
}

.data-table {
  width: 100%;
}

.table-header {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
  padding: 5px 0;
  border-bottom: 1px solid #2a3f5f;
  font-weight: bold;
  color: #00f5ff;
  font-size: 0.8rem;
}

.table-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
  padding: 5px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  font-size: 0.8rem;
}

.table-more {
  padding: 5px 0;
  color: #999;
  font-size: 0.8rem;
  text-align: center;
}

.no-data {
  text-align: center;
  color: #999;
  padding: 2rem 0;
}

.data-editor {
  max-height: 400px;
  overflow-y: auto;
}

.editor-toolbar {
  display: flex;
  gap: 10px;
  margin-bottom: 1rem;
}

.data-table-editor .table-header {
  grid-template-columns: 1fr 1fr 80px;
}

.data-table-editor .table-row {
  grid-template-columns: 1fr 1fr 80px;
  align-items: center;
}

:deep(.el-radio-group) {
  display: flex;
  flex-direction: column;
  gap: 8px;
}
</style>
