<template>
  <el-dialog
    v-model="visible"
    :title="template?.name || '模板预览'"
    width="90%"
    :before-close="handleClose"
    class="template-preview-dialog"
  >
    <div class="preview-container" v-if="template">
      <!-- 模板信息 -->
      <div class="template-info">
        <div class="info-left">
          <div class="template-meta">
            <h2 class="template-title">{{ template.name }}</h2>
            <div class="template-badges">
              <span v-if="template.isNew" class="badge new">新</span>
              <span v-if="template.isHot" class="badge hot">热</span>
              <span v-if="template.isPro" class="badge pro">专业版</span>
            </div>
          </div>
          <p class="template-description">{{ template.description }}</p>
          <div class="template-details">
            <div class="detail-item">
              <span class="label">分类：</span>
              <span class="value">{{ template.categoryName }}</span>
            </div>
            <div class="detail-item">
              <span class="label">尺寸：</span>
              <span class="value">{{ template.width }}×{{ template.height }}</span>
            </div>
            <div class="detail-item">
              <span class="label">使用次数：</span>
              <span class="value">{{ template.usageCount }}</span>
            </div>
            <div class="detail-item">
              <span class="label">作者：</span>
              <span class="value">{{ template.author }}</span>
            </div>
            <div class="detail-item">
              <span class="label">更新时间：</span>
              <span class="value">{{ formatDate(template.updateTime) }}</span>
            </div>
          </div>
          <div class="template-tags">
            <span
              v-for="tag in template.tags"
              :key="tag"
              class="tag"
            >
              {{ tag }}
            </span>
          </div>
        </div>
        <div class="info-right">
          <div class="preview-actions">
            <el-button type="primary" size="large" @click="handleUseTemplate">
              <el-icon><Plus /></el-icon>
              使用此模板
            </el-button>
            <el-button size="large" @click="handleFullscreenPreview">
              <el-icon><FullScreen /></el-icon>
              全屏预览
            </el-button>
            <el-button size="large" @click="handleDownloadTemplate">
              <el-icon><Download /></el-icon>
              下载模板
            </el-button>
          </div>
        </div>
      </div>

      <!-- 预览区域 -->
      <div class="preview-area">
        <div class="preview-toolbar">
          <div class="toolbar-left">
            <span class="preview-title">模板预览</span>
            <span class="scale-info">{{ Math.round(previewScale * 100) }}%</span>
          </div>
          <div class="toolbar-right">
            <el-button-group size="small">
              <el-button @click="zoomOut">
                <el-icon><ZoomOut /></el-icon>
              </el-button>
              <el-button @click="resetZoom">
                <el-icon><ScaleToOriginal /></el-icon>
              </el-button>
              <el-button @click="zoomIn">
                <el-icon><ZoomIn /></el-icon>
              </el-button>
            </el-button-group>
          </div>
        </div>
        
        <div class="preview-canvas-container" ref="canvasContainer">
          <div
            class="preview-canvas"
            :style="canvasStyle"
            ref="previewCanvas"
          >
            <!-- 模板组件预览 -->
            <div
              v-for="component in template.components"
              :key="component.id"
              class="preview-component"
              :style="getComponentStyle(component)"
            >
              <component
                :is="getComponentType(component.type)"
                :config="component"
                :data="{ data: component.data }"
              />
            </div>
            
            <!-- 空模板提示 -->
            <div v-if="!template.components || template.components.length === 0" class="empty-template">
              <el-icon size="60"><Document /></el-icon>
              <p>该模板暂无组件预览</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="handleUseTemplate">
          使用此模板
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import TextComponent from '@/components/widgets/TextComponent.vue'
import ChartComponent from '@/components/widgets/ChartComponent.vue'
import GaugeComponent from '@/components/widgets/GaugeComponent.vue'
import ProgressComponent from '@/components/widgets/ProgressComponent.vue'
import CounterComponent from '@/components/widgets/CounterComponent.vue'
import TableComponent from '@/components/widgets/TableComponent.vue'
import ImageComponent from '@/components/widgets/ImageComponent.vue'
import DefaultComponent from '@/components/widgets/DefaultComponent.vue'

const props = defineProps({
  modelValue: { type: Boolean, default: false },
  template: { type: Object, default: null }
})

const emit = defineEmits(['update:modelValue', 'use'])

// 响应式数据
const canvasContainer = ref(null)
const previewCanvas = ref(null)
const previewScale = ref(0.5)

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const canvasStyle = computed(() => {
  if (!props.template) return {}
  
  return {
    width: props.template.width + 'px',
    height: props.template.height + 'px',
    transform: `scale(${previewScale.value})`,
    transformOrigin: 'top left',
    background: 'linear-gradient(135deg, #0a1a3a 0%, #1a3a8f 100%)',
    position: 'relative',
    border: '1px solid rgba(255, 255, 255, 0.2)',
    borderRadius: '8px',
    overflow: 'hidden'
  }
})

// 监听对话框显示
watch(visible, (show) => {
  if (show && props.template) {
    nextTick(() => {
      calculateOptimalScale()
    })
  }
})

// 方法
const getComponentType = (type) => {
  const componentMap = {
    'text-title': TextComponent,
    'text-content': TextComponent,
    'bar-chart': ChartComponent,
    'line-chart': ChartComponent,
    'pie-chart': ChartComponent,
    'scatter-chart': ChartComponent,
    'radar-chart': ChartComponent,
    'gauge': GaugeComponent,
    'progress': ProgressComponent,
    'counter': CounterComponent,
    'table': TableComponent,
    'image': ImageComponent,
    'video': ImageComponent
  }
  return componentMap[type] || DefaultComponent
}

const getComponentStyle = (component) => ({
  position: 'absolute',
  left: component.x + 'px',
  top: component.y + 'px',
  width: component.width + 'px',
  height: component.height + 'px',
  zIndex: component.zIndex || 1
})

const calculateOptimalScale = () => {
  if (!canvasContainer.value || !props.template) return
  
  const containerRect = canvasContainer.value.getBoundingClientRect()
  const containerWidth = containerRect.width - 40 // 留出边距
  const containerHeight = containerRect.height - 40
  
  const scaleX = containerWidth / props.template.width
  const scaleY = containerHeight / props.template.height
  
  previewScale.value = Math.min(scaleX, scaleY, 1) // 最大不超过100%
}

const zoomIn = () => {
  previewScale.value = Math.min(previewScale.value + 0.1, 2)
}

const zoomOut = () => {
  previewScale.value = Math.max(previewScale.value - 0.1, 0.1)
}

const resetZoom = () => {
  calculateOptimalScale()
}

const handleUseTemplate = () => {
  emit('use', props.template)
}

const handleFullscreenPreview = () => {
  // 全屏预览功能
  if (previewCanvas.value) {
    if (previewCanvas.value.requestFullscreen) {
      previewCanvas.value.requestFullscreen()
    } else if (previewCanvas.value.webkitRequestFullscreen) {
      previewCanvas.value.webkitRequestFullscreen()
    } else if (previewCanvas.value.msRequestFullscreen) {
      previewCanvas.value.msRequestFullscreen()
    }
  }
}

const handleDownloadTemplate = () => {
  // 下载模板功能
  const templateData = JSON.stringify(props.template, null, 2)
  const blob = new Blob([templateData], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = `${props.template.name}.json`
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  URL.revokeObjectURL(url)
  ElMessage.success('模板下载成功')
}

const handleClose = () => {
  visible.value = false
}

const formatDate = (dateString) => {
  return new Date(dateString).toLocaleDateString()
}
</script>

<style scoped>
.template-preview-dialog {
  --el-dialog-margin-top: 5vh;
}

.preview-container {
  height: 80vh;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.template-info {
  display: flex;
  gap: 20px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.info-left {
  flex: 1;
}

.template-meta {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 10px;
}

.template-title {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: #ffffff;
}

.template-badges {
  display: flex;
  gap: 5px;
}

.badge {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 500;
}

.badge.new {
  background: #00ff88;
  color: #000;
}

.badge.hot {
  background: #ff4757;
  color: #fff;
}

.badge.pro {
  background: #ffa502;
  color: #fff;
}

.template-description {
  color: rgba(255, 255, 255, 0.8);
  margin: 0 0 15px 0;
  line-height: 1.5;
}

.template-details {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
  margin-bottom: 15px;
}

.detail-item {
  display: flex;
  gap: 5px;
  font-size: 0.9rem;
}

.label {
  color: rgba(255, 255, 255, 0.6);
  min-width: 80px;
}

.value {
  color: #ffffff;
}

.template-tags {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.tag {
  padding: 4px 12px;
  background: rgba(0, 245, 255, 0.2);
  border: 1px solid rgba(0, 245, 255, 0.3);
  border-radius: 16px;
  font-size: 0.8rem;
  color: #00f5ff;
}

.info-right {
  flex-shrink: 0;
}

.preview-actions {
  display: flex;
  flex-direction: column;
  gap: 10px;
  min-width: 200px;
}

.preview-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: rgba(255, 255, 255, 0.02);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  overflow: hidden;
}

.preview-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background: rgba(255, 255, 255, 0.05);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 15px;
}

.preview-title {
  font-weight: 500;
  color: #ffffff;
}

.scale-info {
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.9rem;
}

.preview-canvas-container {
  flex: 1;
  overflow: auto;
  padding: 20px;
  display: flex;
  justify-content: center;
  align-items: flex-start;
}

.preview-canvas {
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.preview-component {
  pointer-events: none;
}

.empty-template {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: rgba(255, 255, 255, 0.5);
}

.empty-template p {
  margin-top: 10px;
  font-size: 14px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

@media (max-width: 768px) {
  .template-info {
    flex-direction: column;
  }
  
  .template-details {
    grid-template-columns: 1fr;
  }
  
  .preview-actions {
    flex-direction: row;
    min-width: auto;
  }
}

:deep(.el-dialog__body) {
  padding: 20px;
  background: linear-gradient(135deg, #0a1a3a 0%, #1a3a8f 100%);
  color: #ffffff;
}

:deep(.el-dialog__header) {
  background: rgba(255, 255, 255, 0.1);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

:deep(.el-dialog__title) {
  color: #ffffff;
}
</style>
