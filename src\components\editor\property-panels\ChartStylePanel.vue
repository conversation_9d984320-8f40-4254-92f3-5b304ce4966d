<template>
  <div class="chart-style-panel">
    <div class="property-section">
      <h4>图表标题</h4>
      <el-form label-width="80px" size="small">
        <el-form-item label="标题文字">
          <el-input 
            v-model="styleData.title" 
            placeholder="请输入图表标题"
            @change="updateStyle"
          />
        </el-form-item>
        <el-form-item label="标题大小">
          <el-input-number 
            v-model="styleData.titleFontSize" 
            :min="8" 
            :max="48"
            :step="1"
            @change="updateStyle"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="标题颜色">
          <el-color-picker 
            v-model="styleData.titleColor" 
            @change="updateStyle"
            show-alpha
          />
        </el-form-item>
      </el-form>
    </div>
    
    <div class="property-section">
      <h4>图表配色</h4>
      <el-form label-width="80px" size="small">
        <el-form-item label="配色方案">
          <el-select 
            v-model="styleData.colorScheme" 
            @change="updateStyle"
            style="width: 100%"
          >
            <el-option label="科技蓝" value="tech-blue" />
            <el-option label="渐变绿" value="gradient-green" />
            <el-option label="暖色调" value="warm" />
            <el-option label="冷色调" value="cool" />
            <el-option label="彩虹色" value="rainbow" />
            <el-option label="自定义" value="custom" />
          </el-select>
        </el-form-item>
        <el-form-item label="主色调" v-if="styleData.colorScheme === 'custom'">
          <el-color-picker 
            v-model="styleData.primaryColor" 
            @change="updateStyle"
          />
        </el-form-item>
        <el-form-item label="辅助色" v-if="styleData.colorScheme === 'custom'">
          <el-color-picker 
            v-model="styleData.secondaryColor" 
            @change="updateStyle"
          />
        </el-form-item>
      </el-form>
    </div>
    
    <div class="property-section">
      <h4>坐标轴样式</h4>
      <el-form label-width="80px" size="small">
        <el-form-item label="显示X轴">
          <el-switch 
            v-model="styleData.showXAxis" 
            @change="updateStyle"
          />
        </el-form-item>
        <el-form-item label="显示Y轴">
          <el-switch 
            v-model="styleData.showYAxis" 
            @change="updateStyle"
          />
        </el-form-item>
        <el-form-item label="轴线颜色">
          <el-color-picker 
            v-model="styleData.axisColor" 
            @change="updateStyle"
          />
        </el-form-item>
        <el-form-item label="网格线">
          <el-switch 
            v-model="styleData.showGrid" 
            @change="updateStyle"
          />
        </el-form-item>
        <el-form-item label="网格颜色" v-if="styleData.showGrid">
          <el-color-picker 
            v-model="styleData.gridColor" 
            @change="updateStyle"
          />
        </el-form-item>
      </el-form>
    </div>
    
    <div class="property-section">
      <h4>图例设置</h4>
      <el-form label-width="80px" size="small">
        <el-form-item label="显示图例">
          <el-switch 
            v-model="styleData.showLegend" 
            @change="updateStyle"
          />
        </el-form-item>
        <el-form-item label="图例位置" v-if="styleData.showLegend">
          <el-select 
            v-model="styleData.legendPosition" 
            @change="updateStyle"
            style="width: 100%"
          >
            <el-option label="顶部" value="top" />
            <el-option label="底部" value="bottom" />
            <el-option label="左侧" value="left" />
            <el-option label="右侧" value="right" />
          </el-select>
        </el-form-item>
        <el-form-item label="图例颜色" v-if="styleData.showLegend">
          <el-color-picker 
            v-model="styleData.legendColor" 
            @change="updateStyle"
          />
        </el-form-item>
      </el-form>
    </div>
    
    <div class="property-section">
      <h4>动画效果</h4>
      <el-form label-width="80px" size="small">
        <el-form-item label="启用动画">
          <el-switch 
            v-model="styleData.animation" 
            @change="updateStyle"
          />
        </el-form-item>
        <el-form-item label="动画时长" v-if="styleData.animation">
          <div style="display: flex; align-items: center;">
            <el-input-number
              v-model="styleData.animationDuration"
              :min="0"
              :max="10000"
              :step="100"
              @change="updateStyle"
              style="width: 100%"
            />
            <span style="margin-left: 8px; color: #999; font-size: 12px;">毫秒</span>
          </div>
        </el-form-item>
        <el-form-item label="动画缓动" v-if="styleData.animation">
          <el-select 
            v-model="styleData.animationEasing" 
            @change="updateStyle"
            style="width: 100%"
          >
            <el-option label="线性" value="linear" />
            <el-option label="缓入" value="easeIn" />
            <el-option label="缓出" value="easeOut" />
            <el-option label="缓入缓出" value="easeInOut" />
          </el-select>
        </el-form-item>
      </el-form>
    </div>
    
    <div class="property-section" v-if="component.type === 'pie-chart'">
      <h4>饼图特殊设置</h4>
      <el-form label-width="80px" size="small">
        <el-form-item label="内半径">
          <div style="display: flex; align-items: center;">
            <el-input-number
              v-model="styleData.innerRadius"
              :min="0"
              :max="80"
              :step="5"
              @change="updateStyle"
              style="width: 100%"
            />
            <span style="margin-left: 8px; color: #999; font-size: 12px;">%</span>
          </div>
        </el-form-item>
        <el-form-item label="外半径">
          <div style="display: flex; align-items: center;">
            <el-input-number
              v-model="styleData.outerRadius"
              :min="20"
              :max="100"
              :step="5"
              @change="updateStyle"
              style="width: 100%"
            />
            <span style="margin-left: 8px; color: #999; font-size: 12px;">%</span>
          </div>
        </el-form-item>
        <el-form-item label="显示标签">
          <el-switch 
            v-model="styleData.showLabel" 
            @change="updateStyle"
          />
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import { reactive, watch } from 'vue'

const props = defineProps({
  component: { type: Object, required: true }
})

const emit = defineEmits(['update'])

// 响应式数据
const styleData = reactive({
  title: '',
  titleFontSize: 16,
  titleColor: '#ffffff',
  colorScheme: 'tech-blue',
  primaryColor: '#00f5ff',
  secondaryColor: '#1a3a8f',
  showXAxis: true,
  showYAxis: true,
  axisColor: '#2a3f5f',
  showGrid: true,
  gridColor: '#2a3f5f',
  showLegend: true,
  legendPosition: 'right',
  legendColor: '#999999',
  animation: true,
  animationDuration: 1000,
  animationEasing: 'easeOut',
  innerRadius: 30,
  outerRadius: 70,
  showLabel: true
})

// 监听组件变化
watch(() => props.component, (newComponent) => {
  if (newComponent && newComponent.config) {
    const config = newComponent.config
    Object.assign(styleData, {
      title: config.title || '',
      titleFontSize: config.titleFontSize || 16,
      titleColor: config.titleColor || '#ffffff',
      colorScheme: config.colorScheme || 'tech-blue',
      primaryColor: config.primaryColor || '#00f5ff',
      secondaryColor: config.secondaryColor || '#1a3a8f',
      showXAxis: config.showXAxis !== false,
      showYAxis: config.showYAxis !== false,
      axisColor: config.axisColor || '#2a3f5f',
      showGrid: config.showGrid !== false,
      gridColor: config.gridColor || '#2a3f5f',
      showLegend: config.showLegend !== false,
      legendPosition: config.legendPosition || 'right',
      legendColor: config.legendColor || '#999999',
      animation: config.animation !== false,
      animationDuration: config.animationDuration || 1000,
      animationEasing: config.animationEasing || 'easeOut',
      innerRadius: config.innerRadius || 30,
      outerRadius: config.outerRadius || 70,
      showLabel: config.showLabel !== false
    })
  }
}, { immediate: true })

// 方法
const updateStyle = () => {
  emit('update', { 
    config: { 
      ...props.component.config,
      ...styleData 
    }
  })
}
</script>

<style scoped>
.chart-style-panel {
  width: 100%;
}

.property-section {
  margin-bottom: 1.5rem;
}

.property-section h4 {
  margin: 0 0 1rem 0;
  font-size: 0.9rem;
  color: #00f5ff;
  border-bottom: 1px solid #2a3f5f;
  padding-bottom: 0.5rem;
}
</style>
