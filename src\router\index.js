import { createRouter, createWebHistory } from 'vue-router'

// 导入页面组件
import Home from '@/views/Home.vue'
import ProjectList from '@/views/ProjectList.vue'
import Editor from '@/views/Editor.vue'
import Preview from '@/views/Preview.vue'
import DataSource from '@/views/DataSource.vue'
import Templates from '@/views/Templates.vue'

const routes = [
  {
    path: '/',
    name: 'Home',
    component: Home,
    meta: {
      title: '首页'
    }
  },
  {
    path: '/projects',
    name: 'ProjectList',
    component: ProjectList,
    meta: {
      title: '项目列表'
    }
  },
  {
    path: '/editor/:id?',
    name: 'Editor',
    component: Editor,
    meta: {
      title: '可视化编辑器'
    }
  },
  {
    path: '/preview/:id',
    name: 'Preview',
    component: Preview,
    meta: {
      title: '大屏预览'
    }
  },
  {
    path: '/datasource',
    name: 'DataSource',
    component: DataSource,
    meta: {
      title: '数据源管理'
    }
  },
  {
    path: '/templates',
    name: 'Templates',
    component: Templates,
    meta: {
      title: '模板中心'
    }
  },
  {
    path: '/:pathMatch(.*)*',
    redirect: '/'
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - DataV可视化大屏构建系统`
  }
  next()
})

export default router
