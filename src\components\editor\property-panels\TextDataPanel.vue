<template>
  <div class="text-data-panel">
    <div class="property-section">
      <h4>数据源配置</h4>
      <el-form label-width="80px" size="small">
        <el-form-item label="数据类型">
          <el-radio-group v-model="dataConfig.dataType" @change="updateData">
            <el-radio label="static">静态数据</el-radio>
            <el-radio label="dynamic">动态数据</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <template v-if="dataConfig.dataType === 'static'">
          <el-form-item label="文本内容">
            <el-input 
              v-model="dataConfig.staticText" 
              type="textarea"
              :rows="4"
              placeholder="请输入文本内容"
              @change="updateData"
            />
          </el-form-item>
        </template>
        
        <template v-if="dataConfig.dataType === 'dynamic'">
          <el-form-item label="数据源">
            <el-select 
              v-model="dataConfig.dataSourceId" 
              placeholder="选择数据源"
              style="width: 100%"
              @change="updateData"
            >
              <el-option 
                v-for="source in dataSources"
                :key="source.id"
                :label="source.name"
                :value="source.id"
              />
            </el-select>
          </el-form-item>
          
          <el-form-item label="字段映射" v-if="dataConfig.dataSourceId">
            <el-select 
              v-model="dataConfig.textField" 
              placeholder="选择文本字段"
              style="width: 100%"
              @change="updateData"
            >
              <el-option 
                v-for="field in availableFields"
                :key="field"
                :label="field"
                :value="field"
              />
            </el-select>
          </el-form-item>
          
          <el-form-item label="数据过滤">
            <el-input 
              v-model="dataConfig.filter" 
              placeholder="如: value > 100"
              @change="updateData"
            />
          </el-form-item>
          
          <el-form-item label="刷新间隔">
            <el-input-number 
              v-model="dataConfig.refreshInterval" 
              :min="0" 
              :max="3600"
              :step="1"
              @change="updateData"
              style="width: 100%"
            />
            <span style="margin-left: 8px; color: #999; font-size: 12px;">秒 (0为不自动刷新)</span>
          </el-form-item>
        </template>
      </el-form>
    </div>
    
    <div class="property-section">
      <h4>数据格式化</h4>
      <el-form label-width="80px" size="small">
        <el-form-item label="数字格式">
          <el-select 
            v-model="dataConfig.numberFormat" 
            placeholder="选择数字格式"
            style="width: 100%"
            @change="updateData"
          >
            <el-option label="无格式" value="" />
            <el-option label="千分位" value="thousand" />
            <el-option label="百分比" value="percent" />
            <el-option label="货币" value="currency" />
            <el-option label="自定义" value="custom" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="小数位数" v-if="dataConfig.numberFormat">
          <el-input-number 
            v-model="dataConfig.decimalPlaces" 
            :min="0" 
            :max="10"
            :step="1"
            @change="updateData"
            style="width: 100%"
          />
        </el-form-item>
        
        <el-form-item label="前缀文字">
          <el-input 
            v-model="dataConfig.prefix" 
            placeholder="如: ￥"
            @change="updateData"
          />
        </el-form-item>
        
        <el-form-item label="后缀文字">
          <el-input 
            v-model="dataConfig.suffix" 
            placeholder="如: 万元"
            @change="updateData"
          />
        </el-form-item>
      </el-form>
    </div>
    
    <div class="property-section">
      <h4>数据预览</h4>
      <div class="data-preview">
        <div class="preview-label">当前显示内容：</div>
        <div class="preview-content">{{ previewText }}</div>
        <el-button size="small" @click="refreshPreview" style="margin-top: 10px;">
          <el-icon><Refresh /></el-icon>
          刷新预览
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { getDataSourceList } from '@/api/dataSourceApi'

const props = defineProps({
  component: { type: Object, required: true }
})

const emit = defineEmits(['update'])

// 响应式数据
const dataSources = ref([])
const dataConfig = reactive({
  dataType: 'static',
  staticText: '',
  dataSourceId: null,
  textField: '',
  filter: '',
  refreshInterval: 0,
  numberFormat: '',
  decimalPlaces: 2,
  prefix: '',
  suffix: ''
})

// 计算属性
const availableFields = computed(() => {
  // 这里应该根据选中的数据源返回可用字段
  return ['name', 'value', 'label', 'text', 'title']
})

const previewText = computed(() => {
  if (dataConfig.dataType === 'static') {
    return formatText(dataConfig.staticText)
  } else {
    // 这里应该根据数据源和字段映射返回预览文本
    return formatText('示例数据: 12345')
  }
})

// 监听组件变化
watch(() => props.component, (newComponent) => {
  if (newComponent && newComponent.dataConfig) {
    Object.assign(dataConfig, {
      dataType: newComponent.dataConfig.dataType || 'static',
      staticText: newComponent.dataConfig.staticText || newComponent.text || '',
      dataSourceId: newComponent.dataConfig.dataSourceId || null,
      textField: newComponent.dataConfig.textField || '',
      filter: newComponent.dataConfig.filter || '',
      refreshInterval: newComponent.dataConfig.refreshInterval || 0,
      numberFormat: newComponent.dataConfig.numberFormat || '',
      decimalPlaces: newComponent.dataConfig.decimalPlaces || 2,
      prefix: newComponent.dataConfig.prefix || '',
      suffix: newComponent.dataConfig.suffix || ''
    })
  }
}, { immediate: true })

// 方法
const loadDataSources = async () => {
  try {
    const res = await getDataSourceList()
    if (res.code === 200) {
      dataSources.value = res.data.list
    }
  } catch (error) {
    console.error('加载数据源失败:', error)
  }
}

const formatText = (text) => {
  if (!text) return ''
  
  let formattedText = text
  
  // 数字格式化
  if (dataConfig.numberFormat && !isNaN(text)) {
    const num = parseFloat(text)
    switch (dataConfig.numberFormat) {
      case 'thousand':
        formattedText = num.toLocaleString('zh-CN', {
          minimumFractionDigits: dataConfig.decimalPlaces,
          maximumFractionDigits: dataConfig.decimalPlaces
        })
        break
      case 'percent':
        formattedText = (num * 100).toFixed(dataConfig.decimalPlaces) + '%'
        break
      case 'currency':
        formattedText = '￥' + num.toLocaleString('zh-CN', {
          minimumFractionDigits: dataConfig.decimalPlaces,
          maximumFractionDigits: dataConfig.decimalPlaces
        })
        break
    }
  }
  
  // 添加前缀和后缀
  if (dataConfig.prefix) {
    formattedText = dataConfig.prefix + formattedText
  }
  if (dataConfig.suffix) {
    formattedText = formattedText + dataConfig.suffix
  }
  
  return formattedText
}

const updateData = () => {
  emit('update', { 
    dataConfig: { ...dataConfig },
    text: dataConfig.dataType === 'static' ? dataConfig.staticText : undefined
  })
}

const refreshPreview = () => {
  // 刷新数据预览
  console.log('刷新数据预览')
}

// 生命周期
onMounted(() => {
  loadDataSources()
})
</script>

<style scoped>
.text-data-panel {
  width: 100%;
}

.property-section {
  margin-bottom: 1.5rem;
}

.property-section h4 {
  margin: 0 0 1rem 0;
  font-size: 0.9rem;
  color: #00f5ff;
  border-bottom: 1px solid #2a3f5f;
  padding-bottom: 0.5rem;
}

.data-preview {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid #2a3f5f;
  border-radius: 4px;
  padding: 1rem;
}

.preview-label {
  font-size: 0.8rem;
  color: #999;
  margin-bottom: 0.5rem;
}

.preview-content {
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid #2a3f5f;
  border-radius: 4px;
  padding: 0.5rem;
  color: #ffffff;
  font-family: monospace;
  min-height: 40px;
  display: flex;
  align-items: center;
}

:deep(.el-radio-group) {
  display: flex;
  flex-direction: column;
  gap: 8px;
}
</style>
