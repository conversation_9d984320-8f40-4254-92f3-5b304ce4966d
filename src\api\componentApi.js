/**
 * 组件相关API
 */

// 模拟组件分类数据
const componentCategories = [
  {
    id: 'chart',
    name: '图表',
    icon: 'TrendCharts',
    components: [
      {
        id: 'bar-chart',
        name: '柱状图',
        icon: 'Histogram',
        type: 'chart',
        thumbnail: '/images/components/bar-chart.png',
        defaultConfig: {
          width: 400,
          height: 300,
          data: [
            { name: '一月', value: 120 },
            { name: '二月', value: 200 },
            { name: '三月', value: 150 },
            { name: '四月', value: 80 },
            { name: '五月', value: 70 },
            { name: '六月', value: 110 }
          ]
        }
      },
      {
        id: 'line-chart',
        name: '折线图',
        icon: 'DataLine',
        type: 'chart',
        thumbnail: '/images/components/line-chart.png',
        defaultConfig: {
          width: 400,
          height: 300,
          data: [
            { name: '一月', value: 120 },
            { name: '二月', value: 200 },
            { name: '三月', value: 150 },
            { name: '四月', value: 80 },
            { name: '五月', value: 70 },
            { name: '六月', value: 110 }
          ]
        }
      },
      {
        id: 'pie-chart',
        name: '饼图',
        icon: 'PieChart',
        type: 'chart',
        thumbnail: '/images/components/pie-chart.png',
        defaultConfig: {
          width: 400,
          height: 300,
          data: [
            { name: '直接访问', value: 335 },
            { name: '邮件营销', value: 310 },
            { name: '联盟广告', value: 234 },
            { name: '视频广告', value: 135 },
            { name: '搜索引擎', value: 1548 }
          ]
        }
      },
      {
        id: 'scatter-chart',
        name: '散点图',
        icon: 'DataBoard',
        type: 'chart',
        thumbnail: '/images/components/scatter-chart.png',
        defaultConfig: {
          width: 400,
          height: 300,
          data: [
            { name: '系列1', x: 10, y: 20 },
            { name: '系列2', x: 20, y: 36 },
            { name: '系列3', x: 30, y: 10 },
            { name: '系列4', x: 40, y: 50 },
            { name: '系列5', x: 50, y: 25 }
          ]
        }
      },
      {
        id: 'radar-chart',
        name: '雷达图',
        icon: 'Coordinate',
        type: 'chart',
        thumbnail: '/images/components/radar-chart.png',
        defaultConfig: {
          width: 400,
          height: 300,
          data: [
            { name: '销售', value: 80, max: 100 },
            { name: '管理', value: 90, max: 100 },
            { name: '信息技术', value: 70, max: 100 },
            { name: '客服', value: 85, max: 100 },
            { name: '研发', value: 95, max: 100 },
            { name: '市场', value: 75, max: 100 }
          ]
        }
      }
    ]
  },
  {
    id: 'indicator',
    name: '指标',
    icon: 'TrendCharts',
    components: [
      {
        id: 'gauge',
        name: '仪表盘',
        icon: 'Coordinate',
        type: 'gauge',
        thumbnail: '/images/components/gauge.png',
        defaultConfig: {
          width: 300,
          height: 300,
          value: 75,
          max: 100,
          min: 0,
          config: {
            title: '完成率',
            unit: '%',
            precision: 1
          }
        }
      },
      {
        id: 'progress-bar',
        name: '进度条',
        icon: 'Operation',
        type: 'progress',
        thumbnail: '/images/components/progress.png',
        defaultConfig: {
          width: 300,
          height: 60,
          value: 65,
          type: 'horizontal',
          config: {
            label: '项目进度',
            showLabel: true,
            colors: ['#00f5ff', '#1a3a8f']
          }
        }
      },
      {
        id: 'progress-circle',
        name: '环形进度条',
        icon: 'PieChart',
        type: 'progress',
        thumbnail: '/images/components/progress-circle.png',
        defaultConfig: {
          width: 200,
          height: 200,
          value: 80,
          type: 'circle',
          config: {
            label: '完成度',
            showLabel: true,
            size: 160,
            strokeWidth: 8
          }
        }
      },
      {
        id: 'counter',
        name: '数字翻牌器',
        icon: 'DataBoard',
        type: 'counter',
        thumbnail: '/images/components/counter.png',
        defaultConfig: {
          width: 300,
          height: 120,
          value: 12345,
          config: {
            title: '总销售额',
            prefix: '￥',
            suffix: '',
            precision: 0,
            animation: true,
            showTitle: true
          }
        }
      }
    ]
  },
  {
    id: 'text',
    name: '文本',
    icon: 'Document',
    components: [
      {
        id: 'text-title',
        name: '标题',
        icon: 'Document',
        type: 'text',
        thumbnail: '/images/components/text-title.png',
        defaultConfig: {
          width: 200,
          height: 50,
          text: '标题文本',
          fontSize: 24,
          color: '#ffffff',
          fontWeight: 'bold'
        }
      },
      {
        id: 'text-content',
        name: '正文',
        icon: 'Document',
        type: 'text',
        thumbnail: '/images/components/text-content.png',
        defaultConfig: {
          width: 300,
          height: 100,
          text: '这是正文内容',
          fontSize: 16,
          color: '#ffffff',
          fontWeight: 'normal'
        }
      }
    ]
  },
  {
    id: 'media',
    name: '媒体',
    icon: 'Picture',
    components: [
      {
        id: 'image',
        name: '图片',
        icon: 'Picture',
        type: 'media',
        thumbnail: '/images/components/image.png',
        defaultConfig: {
          width: 300,
          height: 200,
          src: '/images/placeholder.jpg',
          alt: '图片'
        }
      },
      {
        id: 'video',
        name: '视频',
        icon: 'VideoPlay',
        type: 'media',
        thumbnail: '/images/components/video.png',
        defaultConfig: {
          width: 400,
          height: 300,
          src: '/videos/placeholder.mp4',
          autoplay: false,
          loop: false
        }
      }
    ]
  },
  {
    id: 'table',
    name: '表格',
    icon: 'Grid',
    components: [
      {
        id: 'data-table',
        name: '数据表格',
        icon: 'Grid',
        type: 'table',
        thumbnail: '/images/components/table.png',
        defaultConfig: {
          width: 600,
          height: 400,
          data: [
            { id: 1, name: '张三', age: 25, city: '北京', score: 95 },
            { id: 2, name: '李四', age: 30, city: '上海', score: 88 },
            { id: 3, name: '王五', age: 28, city: '广州', score: 92 },
            { id: 4, name: '赵六', age: 32, city: '深圳', score: 87 }
          ],
          columns: [
            { key: 'id', title: 'ID', width: '80px', sortable: true },
            { key: 'name', title: '姓名', width: '120px', sortable: true },
            { key: 'age', title: '年龄', width: '80px', sortable: true },
            { key: 'city', title: '城市', width: '120px' },
            { key: 'score', title: '分数', width: '80px', sortable: true, format: 'number' }
          ],
          config: {
            title: '员工信息表',
            showTitle: true,
            showHeader: true,
            showPagination: false
          }
        }
      }
    ]
  },
  {
    id: 'decoration',
    name: '装饰',
    icon: 'MagicStick',
    components: [
      {
        id: 'border',
        name: '边框',
        icon: 'Grid',
        type: 'decoration',
        thumbnail: '/images/components/border.png',
        defaultConfig: {
          width: 400,
          height: 300,
          borderType: 'tech',
          borderColor: '#00f5ff'
        }
      },
      {
        id: 'background',
        name: '背景',
        icon: 'Picture',
        type: 'decoration',
        thumbnail: '/images/components/background.png',
        defaultConfig: {
          width: 800,
          height: 600,
          backgroundType: 'gradient',
          backgroundColor: '#1a3a8f'
        }
      }
    ]
  }
]

/**
 * 获取组件分类列表
 */
export function getComponentCategories() {
  return new Promise(resolve => {
    setTimeout(() => {
      resolve({
        code: 200,
        msg: '获取成功',
        data: componentCategories
      })
    }, 200)
  })
}

/**
 * 根据分类获取组件列表
 * @param {string} categoryId - 分类ID
 */
export function getComponentsByCategory(categoryId) {
  return new Promise(resolve => {
    setTimeout(() => {
      const category = componentCategories.find(cat => cat.id === categoryId)
      if (category) {
        resolve({
          code: 200,
          msg: '获取成功',
          data: category.components
        })
      } else {
        resolve({
          code: 404,
          msg: '分类不存在',
          data: []
        })
      }
    }, 150)
  })
}

/**
 * 获取组件详情
 * @param {string} componentId - 组件ID
 */
export function getComponentDetail(componentId) {
  return new Promise(resolve => {
    setTimeout(() => {
      let component = null
      
      for (const category of componentCategories) {
        const found = category.components.find(comp => comp.id === componentId)
        if (found) {
          component = found
          break
        }
      }
      
      if (component) {
        resolve({
          code: 200,
          msg: '获取成功',
          data: component
        })
      } else {
        resolve({
          code: 404,
          msg: '组件不存在',
          data: null
        })
      }
    }, 100)
  })
}

/**
 * 搜索组件
 * @param {string} keyword - 搜索关键词
 */
export function searchComponents(keyword) {
  return new Promise(resolve => {
    setTimeout(() => {
      const results = []
      
      componentCategories.forEach(category => {
        category.components.forEach(component => {
          if (component.name.includes(keyword) || component.id.includes(keyword)) {
            results.push({
              ...component,
              categoryName: category.name
            })
          }
        })
      })
      
      resolve({
        code: 200,
        msg: '搜索成功',
        data: results
      })
    }, 300)
  })
}
