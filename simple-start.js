// 简化的开发服务器启动脚本
const { spawn } = require('child_process');
const path = require('path');

console.log('🚀 启动 DataV 开发服务器...\n');

// 设置环境变量
process.env.NODE_ENV = 'development';

// 启动 Vite 开发服务器
const viteProcess = spawn('npx', ['vite', '--port', '5177', '--host'], {
  cwd: process.cwd(),
  stdio: 'inherit',
  shell: true
});

viteProcess.on('error', (error) => {
  console.error('❌ 启动失败:', error.message);
  console.log('\n📋 解决方案:');
  console.log('1. 确保已安装 Node.js (版本 16+)');
  console.log('2. 运行: npm install');
  console.log('3. 运行: npm run dev');
});

viteProcess.on('close', (code) => {
  if (code !== 0) {
    console.log(`\n❌ 进程退出，代码: ${code}`);
    console.log('\n📋 请尝试手动启动:');
    console.log('npm run dev');
  }
});

// 处理进程退出
process.on('SIGINT', () => {
  console.log('\n🛑 正在关闭开发服务器...');
  viteProcess.kill('SIGINT');
  process.exit(0);
});

process.on('SIGTERM', () => {
  viteProcess.kill('SIGTERM');
  process.exit(0);
});
